import React, { KeyboardEvent, useEffect, useRef } from 'react';
import { useThemeStyles } from '@/hooks/useThemeStyles';

interface PromptInputProps {
  message: string;
  onChange: (e: React.ChangeEvent<HTMLTextAreaElement>) => void;
  onPaste?: (e: React.ClipboardEvent<HTMLTextAreaElement>) => void;
  onSubmit?: () => void;
}

const PromptInput: React.FC<PromptInputProps> = ({ message, onChange, onPaste, onSubmit }) => {
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const { classes } = useThemeStyles();

  const handleKeyDown = (e: KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
      e.preventDefault();
      if (onSubmit && message.trim()) {
        onSubmit();
      }
    }
  };

  useEffect(() => {
    const textarea = textareaRef.current;
    if (!textarea) return;

    textarea.style.height = 'auto';
    textarea.style.height = `${textarea.scrollHeight}px`;
  }, [message]);

  return (
    <textarea
      ref={textareaRef}
      className={`prompt-input w-full min-h-[24px] py-1 ${classes.backgroundInput} border-none outline-none resize-none ${classes.text} ${classes.placeholder} opacity-70 font-roboto`}
      placeholder="Ask sidekick anything..."
      value={message}
      onChange={onChange}
      onPaste={onPaste}
      onKeyDown={handleKeyDown}
      rows={1}
    />
  );
};

export default PromptInput;

import React, { useState, useEffect, useMemo } from 'react';
import { useParams, useLocation } from 'react-router-dom';
import { useAppSelector } from '@/store/hooks';
import { selectGlobalWorkbookById, selectUserWorkbookById } from '@/store/slices/workbookSlice';
import { patchWorkbookById } from '@/api/workbookApi';
import { RetrievalWorkbook } from '../../workbookTypes';
import { useWorkbookDashboard } from '../../hooks/useWorkbookDashboard';
import { useThemeStyles } from '@/hooks/useThemeStyles';
import { PromptInput } from '@/components/features/chat';
import GreetingSection from '../../components/dashboard/GreetingSection';
import InputLowerTray from '../../components/dashboard/InputLowerTray';
import FileArea from '../../components/dashboard/FileArea';

const OverviewView: React.FC = () => {
  const { workbookId } = useParams<{ workbookId: string }>();
  const { pathname } = useLocation();
  const { classes, colors } = useThemeStyles();

  const isGlobal = pathname.includes('/public/');
  const selectWorkbook = isGlobal ? selectGlobalWorkbookById : selectUserWorkbookById;
  const reduxWorkbook = useAppSelector(state => selectWorkbook(state, workbookId ?? ''));

  const [workbook, setWorkbook] = useState<RetrievalWorkbook | undefined>(reduxWorkbook);
  const [isUpdatingSettings, setIsUpdatingSettings] = useState(false);

  useEffect(() => {
    setWorkbook(reduxWorkbook);
  }, [reduxWorkbook]);

  const styles = {
    container: 'flex flex-col items-center justify-center w-full h-full',
    contentArea: 'flex flex-col items-center w-full',
    inputContainer: `
      flex w-[800px] h-[160px] p-[24px] 
      flex-col justify-between items-center 
      rounded-[32px] border border-[${colors.border}] 
      ${classes.backgroundInput}
    `,
  };

  const handleSendMessage = useMemo(() => (msg: string) => console.log('Send message:', msg), []);

  const { message, handleInputChange, handleSubmit, handleDeleteFile } = useWorkbookDashboard({
    initialFiles: workbook?.files.map(f => f.name) || [],
    onSendMessage: handleSendMessage,
  });

  const handleSettingsSave = async (temperature: number) => {
    if (!workbook) return;

    setIsUpdatingSettings(true);
    try {
      const response = await patchWorkbookById({ id: workbook.id, name: workbook.name, temperature }, isGlobal);
      setWorkbook(prev => (prev ? { ...prev, temperature, updatedUtc: new Date(response.updatedUtc) } : undefined));
    } catch (error) {
      console.error('Failed to update workbook settings:', error);
    } finally {
      setIsUpdatingSettings(false);
    }
  };

  if (!workbook) return null;

  return (
    <div className={styles.container}>
      {/* greeting section */}
      <GreetingSection textColor={classes.text} />

      {/* content area */}
      <div className={styles.contentArea}></div>

      {/* input area */}
      <div className={styles.inputContainer}>
        <PromptInput message={message} onChange={handleInputChange} onSubmit={handleSubmit} />

        <InputLowerTray
          textColor={classes.text}
          onSubmit={handleSubmit}
          workbook={workbook}
          onSettingsSave={handleSettingsSave}
          isUpdatingSettings={isUpdatingSettings}
          isGlobal={isGlobal}
        />
      </div>

      {/* file area */}
      <FileArea files={workbook.files.map(f => f.name)} onDelete={handleDeleteFile} />
    </div>
  );
};

export default React.memo(OverviewView);

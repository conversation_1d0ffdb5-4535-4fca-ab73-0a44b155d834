// =========== Views ===========
export { default as ChatContainer } from './views/ChatContainer';
export { default as ChatWelcomeView } from './views/ChatWelcomeView';
export { default as ChatConversationView } from './views/ChatConversationView';

// =========== Components ===========
export { default as GreetingSection } from './components/welcome/GreetingSection';
export { default as PromptInput } from './components/input/PromptInput';
export { default as PromptSuggestions } from './components/welcome/PromptSuggestions';
export { default as InputLowerTray } from './components/input/InputLowerTray';
export { default as SuggestionButton } from './components/welcome/SuggestionButton';

// =========== Types ===========
export * from './chatTypes';

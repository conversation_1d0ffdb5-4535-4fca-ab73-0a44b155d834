
.chat-settings-modal-overlay {
  position: fixed;
  inset: 0;
  z-index: var(--z-index-backdrop);
  background-color: var(--modal-backdrop-color);
  backdrop-filter: blur(var(--blur-base));
}

.chat-settings-modal {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: var(--z-index-modal);
  width: var(--modal-width-lg);
  display: flex;
  flex-direction: column;
  border-radius: var(--border-radius-border-radius-xxxl);
  background: var(--elements-surface-surface-brand);
  color: var(--text-text-invert);
  box-shadow: var(--shadow-xl);
  outline: none;
  max-height: 90vh;
  overflow: hidden;

  @media (max-width: 1024px) { 
    width: min(var(--modal-width-md), 90vw); 
    border-radius: var(--border-radius-border-radius-xxl); 
  }
  @media (max-height: 700px) { 
    max-height: 85vh; 
  }
}

.chat-settings-modal__close-btn {
  position: absolute;
  top: var(--spacing-spacing-m-2);
  right: var(--spacing-spacing-m-2);
  background: none;
  border: none;
  color: var(--text-text-invert);
  cursor: pointer;
  padding: var(--spacing-spacing-sm-2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all var(--transition-normal) ease;

  &:hover {
    background: var(--modal-hover-background);
    opacity: var(--opacity-80);
  }

  &:focus-visible {
    outline: var(--border-weight-border-weight-m) solid var(--text-text-invert);
    outline-offset: var(--border-weight-border-weight-m);
  }
}

.chat-settings-modal__content {
  padding: var(--spacing-spacing-l-1) var(--spacing-spacing-l-3) var(--spacing-spacing-m-2);
  display: flex;
  flex-direction: column;
  overflow-y: auto;

  @media (max-width: 1024px) { 
    padding: var(--spacing-spacing-l-2) var(--spacing-spacing-m-3) var(--spacing-spacing-m-1); 
  }
}

.chat-settings-modal__title {
  font-family: var(--font-sofia-pro);
  font-size: var(--font-size-4xl);
  font-weight: var(--font-weight-semibold);
  color: var(--text-text-invert);
  margin-bottom: var(--spacing-spacing-l-1);
}

/* ===== TEMPERATURE SECTION ===== */
.chat-settings-modal__temperature-section {
  display: flex;
  flex-direction: column;
  width: 100%; 
}

.chat-settings-modal__temperature-title {
  height: var(--section-title-height);
  color: var(--neutral-white);
  font-family: var(--font-sofia-pro);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-bold);
  font-style: normal;
  line-height: 1.3;
  margin-bottom: var(--spacing-spacing-sm-3);
}

.chat-settings-modal__temperature-description {
  max-width: 450px;
  color: var(--brand-hmk-primary-200);
  font-family: var(--font-roboto);
  font-size: var(--font-size-sm);
  font-style: normal;
  font-weight: var(--font-weight-normal);
  line-height: 1.2;
  margin: 0 0 var(--spacing-spacing-sm-3) 0;
}

.chat-settings-modal__radio-group {
  margin-bottom: var(--spacing-spacing-m-3);
}

.chat-settings-modal__radio-option {
  display: flex;
  align-items: center;
  gap: var(--spacing-spacing-sm-3);
  cursor: pointer;
  color: var(--text-text-invert);
  font-family: var(--font-roboto);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-normal);
  transition: all var(--transition-normal) ease;

  &:hover {
    opacity: var(--opacity-80);
  }
}

.chat-settings-modal__radio-input {
  display: flex;
  width: var(--radio-size);
  height: var(--radio-size);
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  border: var(--border-weight-border-weight-m) solid var(--text-text-invert);
  background-color: transparent;
  transition: background-color var(--transition-normal) ease;

  &:focus-visible {
    outline: var(--border-weight-border-weight-m) solid var(--button-button-primary);
    outline-offset: var(--border-weight-border-weight-m);
  }
}

.chat-settings-modal__radio-indicator {
  display: flex;
  align-items: center;
  justify-content: center;

  &[data-unchecked] {
    display: none;
  }

  &::before {
    content: '';
    width: var(--spacing-spacing-m-1);
    height: var(--spacing-spacing-m-1);
    border-radius: 50%;
    background-color: var(--text-text-invert);
  }
}

/* ===== SYSTEM INSTRUCTIONS SECTION ===== */
.chat-settings-modal__system-instructions {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.chat-settings-modal__system-instructions-title {
  height: var(--section-title-height);
  color: var(--neutral-white);
  font-family: var(--font-sofia-pro);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-bold);
  font-style: normal;
  line-height: 1.3;
  margin-bottom: var(--spacing-spacing-sm-3);
}

.chat-settings-modal__system-instructions-description {
  max-width: 450px;
  color: var(--brand-hmk-primary-200);
  font-family: var(--font-roboto);
  font-size: var(--font-size-sm);
  font-style: normal;
  font-weight: var(--font-weight-normal);
  line-height: 1.2;
  margin: 0 0 8px 0;
}

/* ===== INSTRUCTION TYPE SELECTION ===== */
.chat-settings-modal__instruction-type-section {
  margin-bottom: 8px;
}

.chat-settings-modal__instruction-type-options {
  display: flex;
  gap: var(--spacing-spacing-m-3);
}

.chat-settings-modal__instruction-type-option {
  display: flex;
  align-items: center;
  gap: var(--spacing-spacing-sm-3);
  cursor: pointer;
  color: var(--text-text-invert);
  font-family: var(--font-roboto);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-normal);
  transition: all var(--transition-normal) ease;

  &:hover {
    opacity: var(--opacity-80);
  }
}

.chat-settings-modal__textarea-container {
  position: relative;
  width: 100%;
}

.chat-settings-modal__textarea {
  width: 100%;
  height: var(--component-height-xl);
  padding: var(--spacing-spacing-sm-3) var(--spacing-spacing-m-3) var(--spacing-spacing-m-3) var(--spacing-spacing-m-3);
  border: var(--border-weight-border-weight-s) solid var(--brand-hmk-shades-400);
  border-radius: var(--border-radius-border-radius-m);
  background: var(--modal-input-background);
  color: var(--text-text-invert);
  font-family: var(--font-roboto);
  font-size: var(--font-size-base);
  outline: none;
  transition: border-color var(--transition-normal) ease;
  resize: none;

  &:focus {
    border-color: var(--border-color-border-highlight);
  }

  &::placeholder {
    color: var(--modal-placeholder-color);
  }
}

.chat-settings-modal__textarea-icon {
  position: absolute;
  right: var(--spacing-spacing-m-1);
  top: var(--spacing-spacing-m-1);
  color: var(--text-text-invert);
  width: var(--spacing-spacing-m-3);
  height: var(--spacing-spacing-m-3);
}

/* ===== FOOTER ACTIONS ===== */
.chat-settings-modal__footer {
  border-top: var(--border-weight-border-weight-s) solid var(--brand-hmk-primary-400);
  padding: var(--spacing-spacing-m-3) var(--spacing-spacing-l-3);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chat-settings-modal__button {
  padding: var(--spacing-spacing-sm-3) var(--spacing-spacing-m-2);
  display: flex;
  align-items: center;
  gap: var(--spacing-spacing-sm-3);
  border-radius: var(--border-radius-border-radius-full);
  font-family: var(--font-roboto);
  font-size: var(--font-size-base);
  font-style: normal;
  font-weight: var(--font-weight-normal);
  text-align: center;
  cursor: pointer;
  transition: all var(--transition-normal) ease;
  
  &:disabled {
    opacity: var(--opacity-60);
    cursor: not-allowed;
  }
}

.chat-settings-modal__button--cancel {
  border: 1.5px solid var(--brand-hmk-primary-400);
  background: transparent;
  color: var(--text-text-invert);
  
  &:hover:not(:disabled) {
    background: var(--modal-hover-background);
  }
}

.chat-settings-modal__button--save {
  border: var(--border-weight-border-weight-s) solid var(--brand-hmk-primary-400);
  background: var(--brand-hmk-primary-400);
  color: var(--text-text-invert);
  
  &:hover:not(:disabled) {
    background: var(--button-hover-primary);
  }
} 

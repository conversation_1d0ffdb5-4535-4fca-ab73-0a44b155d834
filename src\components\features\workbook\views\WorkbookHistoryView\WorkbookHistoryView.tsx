import React, { useEffect, useState, useMemo } from 'react';
import { useParams } from 'react-router-dom';
import { getWorkbookSessions } from '@/api/workbookApi';
import { WorkbookHistorySummary, WorkbookSessionsResponse } from '../../workbookTypes';
import WorkbookHistoryItem from '../../components/history/WorkbookHistoryItem';
import NoWorkbookHistory from '../../components/history/NoWorkbookHistory';
import WorkbookContainer from '../../layouts/WorkbookContainer';
import { useThemeStyles } from '@hooks/useThemeStyles';
import { processWorkbookHistoryTime } from '@/utils/dateUtils';
import {
  buildWorkbookResumeSessionPath,
  extractLatestMessage,
} from '@/components/features/workbook/utils/workbookUtils';

interface WorkbookHistoryViewProps {
  isGlobal: boolean;
}

const WorkbookHistoryView: React.FC<WorkbookHistoryViewProps> = ({ isGlobal }) => {
  const { workbookId } = useParams<{ workbookId: string }>();
  const { classes } = useThemeStyles();
  const [historyItems, setHistoryItems] = useState<WorkbookHistorySummary[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadHistory = async () => {
      if (!workbookId) {
        setError('Workbook ID is required');
        setIsLoading(false);
        return;
      }

      setIsLoading(true);
      setError(null);

      try {
        const data: WorkbookSessionsResponse = await getWorkbookSessions(workbookId, isGlobal);

        if (!data || !Array.isArray(data) || data.length === 0) {
          setHistoryItems([]);
          return;
        }

        // Use flatMap to avoid map + filter combination
        const transformedData: WorkbookHistorySummary[] = data.flatMap(session => {
          const timeData = processWorkbookHistoryTime(session.createdUtc, session.updatedUtc);

          if (!timeData) {
            return []; // Skip invalid dates
          }

          const latestMessage = extractLatestMessage(session.messages);

          return [
            {
              id: session.id,
              title: timeData.title,
              createdTitle: timeData.createdTitle,
              timestamp: session.createdUtc,
              latestMessage,
              resumeUrl: buildWorkbookResumeSessionPath(workbookId, session.id, isGlobal),
            },
          ];
        });

        setHistoryItems(transformedData);
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Failed to load session history';
        setError(errorMessage);
      } finally {
        setIsLoading(false);
      }
    };

    loadHistory();
  }, [workbookId, isGlobal]);

  const containerStyles = useMemo(() => {
    const baseStyles =
      'workbook-history-container flex flex-col flex-grow pt-[24px] pb-[40px] gap-[40px] w-full h-full max-w-[760px] mx-auto';
    const centeringStyles =
      isLoading || error || historyItems.length === 0 ? 'items-center justify-center' : 'items-center';

    return `${baseStyles} ${classes.background} ${centeringStyles}`;
  }, [classes.background, isLoading, error, historyItems.length]);

  // Conditional Rendering Logic
  const renderContent = () => {
    if (isLoading) {
      return <p className={`workbook-history-container__loading-text ${classes.text}`}>Loading session history...</p>;
    }

    if (error) {
      return (
        <div className="workbook-history-container__error-message text-center p-8 rounded-lg text-red-500 dark:text-red-400">
          <h2 className={`workbook-history-container__error-heading text-xl font-semibold mb-2 ${classes.text}`}>
            Error Loading History
          </h2>
          <p className="workbook-history-container__error-details">{error}</p>
        </div>
      );
    }

    if (historyItems.length === 0) {
      return <NoWorkbookHistory />;
    }

    return historyItems.map(item => <WorkbookHistoryItem key={item.id} item={item} />);
  };

  return (
    <WorkbookContainer>
      <div className={containerStyles}>{renderContent()}</div>
    </WorkbookContainer>
  );
};

export default WorkbookHistoryView;

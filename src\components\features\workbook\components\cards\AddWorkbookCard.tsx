import React from 'react';
import { IoMdAdd } from 'react-icons/io';
import { useThemeStyles } from '@hooks/useThemeStyles';
import { AddWorkbookCardProps } from '../../workbookTypes';

const AddWorkbookCard: React.FC<AddWorkbookCardProps> = ({ onClick, isGlobal }) => {
  const { classes, colors } = useThemeStyles();

  const styles = {
    card: `
      flex w-[240px] h-[300px] 
      rounded-[12px] 
      border border-[${colors.border}]
      ${classes.backgroundInput}
      cursor-pointer
      hover:opacity-80
      transition-opacity
      focus:outline-none
      focus:ring-2
      focus:ring-blue-500
    `,
    contentContainer: "flex-1 flex flex-col items-center justify-center gap-4",
    icon: `w-12 h-12 ${classes.text}`,
    text: `font-roboto text-[16px] font-medium ${classes.text}`
  };


  return (
    <div
      className={styles.card}
      onClick={onClick}
      role="button"
      aria-label="Add new workbook"
    >
      <div className={styles.contentContainer}>
        <IoMdAdd className={styles.icon} aria-hidden="true" />
        <span className={styles.text}>
          Start a new {isGlobal ? 'Public' : ''} Workbook
        </span>
      </div>
    </div>
  );
};

export default AddWorkbookCard;

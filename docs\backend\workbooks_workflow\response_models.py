from typing import List, Annotated, Optional
from datetime import datetime

from pydantic import Field, StringConstraints

from models.SidekickBaseModel import SidekickBaseModel
from models.retrieval_workbook.RetrievalWorkbook import RetrievalWorkbook, RetrievalWorkbookSummary
from models.retrieval_workbook.RetrievalFile import RetrievalFileSignedUrl

class CreateRetrievalWorkbookResponse(SidekickBaseModel):
    workbook: RetrievalWorkbook
    # files_signed_urls: List[RetrievalFileSignedUrl] = Field(default_factory=list)

    def to_dict(self, date_format_iso=True, include_document_id=False, to_camel=False):
        mode = 'json' if date_format_iso else 'python'
        exclude = {}
        if not include_document_id:
            exclude['id'] = True

        return self.model_dump(mode=mode, exclude=exclude, by_alias=to_camel)
    
class GetRetrievalWorkbooksResponse(SidekickBaseModel):
    user: str | None
    workbooks: List[RetrievalWorkbookSummary] = Field(default_factory=list)

    def to_dict(self, date_format_iso=True, include_document_id=False, to_camel=False):
        mode = 'json' if date_format_iso else 'python'
        exclude = {}
        if not include_document_id:
            exclude['id'] = True

        return self.model_dump(mode=mode, exclude=exclude, by_alias=to_camel)
    
class UpdateRetrievalWorkbookResponse(SidekickBaseModel):
    id: str
    name: Annotated[str, StringConstraints(strict=True, strip_whitespace=True, min_length=1, max_length=255)]
    description: Annotated[Optional[str], StringConstraints(strip_whitespace=True, min_length=0, max_length=1024)] = None
    systemInstructions: Annotated[Optional[str], StringConstraints(strip_whitespace=True)] = None
    temperature: Annotated[Optional[float], Field(
        strict=True,
    )] = None
    updated_utc: datetime

    def to_dict(self, date_format_iso=True, include_document_id=False, to_camel=False):
        mode = 'json' if date_format_iso else 'python'
        exclude = {}
        if not include_document_id:
            exclude['id'] = True

        return self.model_dump(mode=mode, exclude=exclude, by_alias=to_camel)

import React from 'react';

interface SidekickWorkbookIconProps extends React.SVGProps<SVGSVGElement> {
  size?: number | string;
}

const SidekickWorkbookIcon: React.FC<SidekickWorkbookIconProps> = ({ size, width, height, ...props }) => {
  return (
    <svg
      width={width ?? size ?? 28}
      height={height ?? size ?? 28}
      viewBox="0 0 28 28"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M20.9993 2.33301H6.99935C5.71602 2.33301 4.66602 3.38301 4.66602 4.66634V23.333C4.66602 24.6163 5.71602 25.6663 6.99935 25.6663H20.9993C22.2827 25.6663 23.3327 24.6163 23.3327 23.333V4.66634C23.3327 3.38301 22.2827 2.33301 20.9993 2.33301ZM10.4993 4.66634H12.8327V10.4997L11.666 9.62467L10.4993 10.4997V4.66634ZM20.9993 23.333H6.99935V4.66634H8.16602V15.1663L11.666 12.5413L15.166 15.1663V4.66634H20.9993V23.333Z"
        fill="currentColor"
      />
    </svg>
  );
};

export default SidekickWorkbookIcon;

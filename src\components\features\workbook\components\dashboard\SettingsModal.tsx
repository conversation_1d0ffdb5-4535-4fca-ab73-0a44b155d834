import React, { useState, useEffect } from 'react';
import { IoClose } from 'react-icons/io5';
import { MdOutlineSave } from 'react-icons/md';
import { Dialog } from '@base-ui-components/react/dialog';
import { Radio } from '@base-ui-components/react/radio';
import { RadioGroup } from '@base-ui-components/react/radio-group';
import { RetrievalWorkbook } from '@features/workbook/workbookTypes';
import './SettingsModal.scss';

interface SettingsModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (temperature: number) => Promise<void>;
  workbook?: RetrievalWorkbook;
  isLoading?: boolean;
}

type TemperatureId = 'less-creative' | 'classic' | 'more-creative';

// Centralized configuration for temperature options
const temperatureOptions: { id: TemperatureId; value: number; displayName: string }[] = [
  { id: 'less-creative', value: 0.0, displayName: 'Less Creative' },
  { id: 'classic', value: 1.0, displayName: 'Classic' },
  { id: 'more-creative', value: 2.0, displayName: 'More Creative' },
];

const defaultTemperatureOption = temperatureOptions[0]; // 'less-creative'

// Temperature conversion functions - simplified using the centralized config
export const temperatureValueToOption = (value: number): TemperatureId => {
  const option = temperatureOptions.find(opt => opt.value === value);
  return option ? option.id : defaultTemperatureOption.id;
};

export const temperatureOptionToValue = (optionId: TemperatureId): number => {
  const option = temperatureOptions.find(opt => opt.id === optionId);
  return option ? option.value : defaultTemperatureOption.value;
};

export const getTemperatureDisplayName = (value: number): string => {
  const option = temperatureOptions.find(opt => opt.value === value);
  return option ? option.displayName : defaultTemperatureOption.displayName;
};

const SettingsModal: React.FC<SettingsModalProps> = ({ isOpen, onClose, onSave, workbook, isLoading = false }) => {
  const [selectedTemperature, setSelectedTemperature] = useState<TemperatureId>('less-creative');
  const [isSaving, setIsSaving] = useState(false);

  // Initialize form with workbook data when modal opens
  useEffect(() => {
    if (isOpen && workbook) {
      setSelectedTemperature(temperatureValueToOption(workbook.temperature));
    }
  }, [isOpen, workbook]);

  const handleSave = async () => {
    if (isSaving) return;

    setIsSaving(true);
    try {
      const temperatureValue = temperatureOptionToValue(selectedTemperature);
      await onSave(temperatureValue);
      onClose();
    } catch (error) {
      console.error('Failed to save settings:', error);
    } finally {
      setIsSaving(false);
    }
  };

  const handleCancel = () => {
    // Reset to workbook values or defaults
    if (workbook) {
      setSelectedTemperature(temperatureValueToOption(workbook.temperature));
    } else {
      setSelectedTemperature('less-creative');
    }
    onClose();
  };

  return (
    <Dialog.Root open={isOpen} onOpenChange={onClose}>
      <Dialog.Portal>
        <Dialog.Backdrop className="settings-modal-overlay" />
        <Dialog.Popup className="settings-modal">
          {/* Close Button */}
          <Dialog.Close className="settings-modal__close-btn" aria-label="Close modal">
            <IoClose className="w-6 h-6" />
          </Dialog.Close>

          {/* Content */}
          <div className="settings-modal__content">
            {/* Title */}
            <Dialog.Title className="settings-modal__title">Setting</Dialog.Title>

            {/* Temperature Section */}
            <div className="settings-modal__temperature-section">
              {/* Temperature Title */}
              <h2 className="settings-modal__temperature-title">Temperature</h2>

              {/* Temperature Description */}
              <div className="settings-modal__temperature-description">
                Temperature is a parameter that allows you to control the creativity of Sidekick's generated output.
              </div>

              {/* Radio Button Group */}
              <div className="settings-modal__radio-group">
                <RadioGroup
                  value={selectedTemperature}
                  onValueChange={value => setSelectedTemperature(value as TemperatureId)}
                  className="flex items-center gap-6"
                >
                  {temperatureOptions.map(option => (
                    <label key={option.id} className="settings-modal__radio-option">
                      <Radio.Root value={option.id} className="settings-modal__radio-input">
                        <Radio.Indicator className="settings-modal__radio-indicator" />
                      </Radio.Root>
                      <span>{option.displayName}</span>
                    </label>
                  ))}
                </RadioGroup>
              </div>
            </div>

            {/* Buttons */}
            <div className="settings-modal__actions">
              <button onClick={handleCancel} className="settings-modal__button settings-modal__button--cancel">
                Cancel
              </button>
              <button
                onClick={handleSave}
                disabled={isSaving || isLoading}
                className="settings-modal__button settings-modal__button--save"
              >
                <MdOutlineSave className="w-5 h-5" />
                <span>{isSaving ? 'Saving...' : 'Save Settings'}</span>
              </button>
            </div>
          </div>
        </Dialog.Popup>
      </Dialog.Portal>
    </Dialog.Root>
  );
};

export default SettingsModal;

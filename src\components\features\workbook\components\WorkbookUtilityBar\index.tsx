import type { RetrievalWorkbookUpdate, RetrievalSession, RetrievalSessionQuery, RetrievalSessionAnswer } from '../../workbookTypes';

import React from 'react';
import { RetrievalWorkbook } from '../../workbookTypes';
import { MdOutlineModeEdit } from 'react-icons/md';
import { FaPlus } from 'react-icons/fa';
import { FaCheck } from 'react-icons/fa6';
import { RxCross2 } from 'react-icons/rx';
import { DotLoader } from 'react-spinners';
import { useNavigate } from 'react-router-dom';

import { useAppDispatch, useAppSelector } from '@/store/hooks';
import {
  updateWorkbookById,
  selectUserWorkbookPendingStatus,
  selectGlobalWorkbookPendingStatus,
  selectMockLoadingFiles,
  createMyWorkbookSession,
  selectUserWorkbookCreatingSessionStatus,
  selectGlobalWorkbookCreatingSessionStatus,
  selectLatestWorkbookSession,
} from '@/store/slices/workbookSlice';
import { selectCurrentUser } from '@/store/slices/authSlice';
import { handleFileChangeWithAttestation } from '@/utils/ucdFileUploadWrapper';
import { buildWorkbookBasePath } from '../../utils/workbookUtils';

import WorkbookUtilityFile from './WorkbookUtilityFile';

import useThemeStyles from '@hooks/useThemeStyles';
import './styles.scss';

// Helper function to check if a session is empty (no meaningful messages)
const isSessionEmpty = (session: RetrievalSession | null): boolean => {
  if (!session || !session.messages || session.messages.length === 0) {
    return true;
  }
  
  // Check if there are any user messages (queries)
  const userMessages = session.messages.filter((msg: RetrievalSessionQuery | RetrievalSessionAnswer) => 'query' in msg);
  return userMessages.length === 0;
};

// Helper function to check if we should allow creating a new session
const shouldAllowNewSession = (workbook: RetrievalWorkbook | undefined, latestSession: RetrievalSession | null): boolean => {
  // If no workbook, don't allow
  if (!workbook) return false;
  
  // If no sessions exist, allow creating the first one
  if (!workbook.sessions || workbook.sessions.length === 0) return true;
  
  // If latest session is empty, don't allow creating another empty session
  return isSessionEmpty(latestSession);
};

export type WorkbookUtilityBarProps = {
  isGlobal: boolean;
  workbook?: RetrievalWorkbook;
  onFileUpload?: (files: File[]) => void;
};

const WorkbookUtilityBar: React.FC<WorkbookUtilityBarProps> = (props: WorkbookUtilityBarProps) => {
  const { isGlobal, workbook, onFileUpload } = props;
  const { classes, colors: themeColors } = useThemeStyles();
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const pendingStatus = isGlobal ? selectGlobalWorkbookPendingStatus : selectUserWorkbookPendingStatus;
  const workbookPendingStatus = useAppSelector(state => pendingStatus(state, workbook?.id ?? ''));
  const mockLoadingFiles = useAppSelector(state => selectMockLoadingFiles(state, workbook?.id ?? ''));
  const currentUser = useAppSelector(selectCurrentUser);
  
  // Add session creation status selectors
  const isCreatingUserSession = useAppSelector(state =>
    selectUserWorkbookCreatingSessionStatus(state, workbook?.id ?? '')
  );
  const isCreatingGlobalSession = useAppSelector(state =>
    selectGlobalWorkbookCreatingSessionStatus(state, workbook?.id ?? '')
  );
  
  const isCreatingSession = isGlobal ? isCreatingGlobalSession : isCreatingUserSession;
  
  // Get the latest session to check if it's empty
  const latestSession = useAppSelector(state => 
    selectLatestWorkbookSession(state, workbook?.id ?? '', isGlobal)
  );
  
  const isLatestSessionEmpty = isSessionEmpty(latestSession);
  const userIsAuthor = Boolean(workbook?.author) && workbook?.author === currentUser;
  
  // Determine if we should allow creating a new session
  const allowNewSession = shouldAllowNewSession(workbook, latestSession);
  
  // Determine if the "New chat session" button should be disabled
  const isNewSessionDisabled = isCreatingSession || !allowNewSession;

  const [editingWorkbookName, setEditingWorkbookName] = React.useState(false);
  const [updateWorkbookName, setUpdateWorkbookName] = React.useState(workbook?.name ?? '');

  const filesToRender = () => {
    const workbookFiles = (workbook?.files ?? []).slice();
    const mockFiles = mockLoadingFiles.slice();

    for (const mockFile of mockFiles) {
      if (!workbookFiles.find(f => f.name === mockFile.name)) {
        workbookFiles.push(mockFile);
      }
    }
    workbookFiles.sort((fileA, fileB) => fileA.name.localeCompare(fileB.name));
    return workbookFiles;
  };

  const onFilesChanged = async (event: React.ChangeEvent<HTMLInputElement>) => {
    if (!onFileUpload) return;

    await handleFileChangeWithAttestation(event, 'workbook', currentUser, onFileUpload);
  };

  const onUpdateWorkbookNameChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
    setUpdateWorkbookName(event.currentTarget.value);
  };

  const onToggleEditWorkbookName = async (isEditing: boolean) => {
    setUpdateWorkbookName(isEditing ? (workbook?.name ?? '') : '');
    setEditingWorkbookName(isEditing);
  };

  const onWorkbookNameSave = async () => {
    if (workbook && workbook.name !== updateWorkbookName && workbook.author == currentUser) {
      const lastUpdated = workbook.updatedUtc;
      const workbookUpdates: RetrievalWorkbookUpdate = {
        id: workbook.id,
        name: updateWorkbookName,
        clear_description: false,
      };
      const updateResult = await dispatch(updateWorkbookById({ workbookUpdates, isGlobal })).unwrap();
      if (updateResult.updatedUtc !== lastUpdated) {
        setEditingWorkbookName(false);
      }
    }
  };

  const handleCreateNewSession = async () => {
    // Prevent multiple concurrent session creation attempts
    // Also prevent creating new session if not allowed
    if (!workbook || isCreatingSession || !allowNewSession) {
      return;
    }

    try {
      await dispatch(createMyWorkbookSession({ workbookId: workbook.id, isGlobal: isGlobal })).unwrap();
      // Navigate to workbook without sessionId to show the new session
      const workbookPath = buildWorkbookBasePath(workbook.id, isGlobal);
      navigate(workbookPath);
    } catch (error) {
      console.error('Failed to create new session:', error);
      // You could add a toast notification here for user feedback
    }
  };

  const WorkbookName = (
    <>
      <div className={`workbook-name-text ${classes.text}`}>{workbook?.name ?? ''}</div>
      {userIsAuthor && (
        <div className="workbook-name-edit" onClick={() => onToggleEditWorkbookName(true)}>
          <MdOutlineModeEdit fill={'#ECF7FF'} size={16} />
        </div>
      )}
    </>
  );

  const WorkbookNameEdit = (
    <>
      <div className="workbook-name-edit-input bg-white border-2 border-[#0066B1] w-full p-2 rounded">
        <input
          onChange={onUpdateWorkbookNameChange}
          value={updateWorkbookName ?? ''}
          className="text-black w-full outline-none"
        ></input>
      </div>
      {(workbookPendingStatus?.isUpdating ?? false) ? (
        <DotLoader loading={true} color="#ECF7FF" size={16} />
      ) : (
        <>
          <div className="workbook-name-edit-save" onClick={() => onWorkbookNameSave()}>
            <FaCheck fill={'#ECF7FF'} size={16} />
          </div>
          <div className="workbook-name-edit-cancel" onClick={() => onToggleEditWorkbookName(false)}>
            <RxCross2 color="#F7987D" size={16} />
          </div>
        </>
      )}
    </>
  );

  return (
    <div className="workbook-utility-bar-main">
      <div className="workbook-name">{editingWorkbookName ? WorkbookNameEdit : WorkbookName}</div>
      <div className="workbook-description"></div>
      <div className="workbook-files">
        {filesToRender().map(file => {
          return (
            <WorkbookUtilityFile
              key={file.name}
              workbookId={workbook?.id}
              file={file}
              isGlobal={isGlobal}
              allowDelete={userIsAuthor}
            />
          );
        })}
      </div>
      <div className="workbook-actions">
        {userIsAuthor && (
          <div className="workbook-add-file">
            <label
              htmlFor="uploadWorkbookFiles"
              className={`workbook-add-file-label ${themeColors.text} hover:bg-[#0066B1] hover:cursor-pointer`}
            >
              <div className="workbook-add-file-icon">
                <FaPlus fill="white" />
              </div>
              <div className={`workbook-add-file-text ${classes.text} font-roboto`}>Upload files</div>
            </label>
            <input id="uploadWorkbookFiles" type="file" multiple hidden onChange={onFilesChanged} accept=".txt, .pdf" />
          </div>
        )}
        <div 
          className={`workbook-new-session ${
            isNewSessionDisabled 
              ? 'opacity-50 cursor-not-allowed' 
              : 'hover:bg-[#0066B1] hover:cursor-pointer'
          }`} 
          onClick={isNewSessionDisabled ? undefined : handleCreateNewSession}
          title={
            isCreatingSession 
              ? 'Creating new session...' 
              : !allowNewSession 
                ? 'Start chatting in the current session first'
                : 'Create a new chat session'
          }
        >
          <div className={`workbook-new-session-text ${classes.text} flex items-center gap-2`}>
            {isCreatingSession && <DotLoader color="white" size={16} />}
            {isCreatingSession 
              ? 'Creating session...' 
              : !allowNewSession 
                ? 'Current session active'
                : 'New chat session'
            }
          </div>
        </div>
      </div>
    </div>
  );
};

export default WorkbookUtilityBar;

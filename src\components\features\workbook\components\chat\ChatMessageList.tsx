import type {
  RetrievalCitation,
  RetrievalCitationSource,
  RetrievalSessionAnswer,
  RetrievalSessionQuery,
} from '@/types';
import type { ChatMessageListProps } from '@/types/layout';

import React from 'react';
import { useAppDispatch } from '@/store/hooks';
import { useThemeStyles } from '@/hooks/useThemeStyles';
import clsx from 'clsx';

import { updateMessageFeedback } from '@/store/slices/workbookSlice';

import ChatMessage from './ChatMessage';

/**
 * ChatMessageList - Component to display a list of chat messages
 *
 * - Renders a list of chat messages with proper spacing
 * - Uses simple scrolling container
 * - Shows loading indicators for message processing
 */
const ChatMessageList: React.FC<ChatMessageListProps> = ({
  workbook,
  session,
  messages,
  loading = false,
  isGlobal = false,
}) => {
  const { classes } = useThemeStyles();
  const dispatch = useAppDispatch();
  const block = 'workbook-message-list';

  const handleMessageFeedback = async (
    workbookId: string,
    sessionId: string,
    messageId: string,
    feedback: 0 | 1,
    isGlobal: boolean
  ) => {
    await dispatch(
      updateMessageFeedback({
        workbookId: workbookId,
        sessionId: sessionId,
        messageId: messageId,
        feedback: feedback,
        isGlobal: isGlobal,
      })
    );
  };

  return (
    <div className={clsx(`${block}__items-container`, 'flex flex-col space-y-3 w-full pt-4 pb-4')}>
      {messages.map((msg, index) => {
        let message = '';
        let citations: RetrievalCitation[] = [];
        let citationSources: RetrievalCitationSource[] = [];

        // Type guard to check if it's a query or answer
        if ('query' in msg) {
          // It's a user message
          message = (msg as RetrievalSessionQuery).query;
        } else if ('answer' in msg) {
          // It's a model message
          const answer = msg as RetrievalSessionAnswer;
          message = answer.answer;
          citations = answer.citations || [];
          citationSources = answer.citationSources || [];
        } else {
          console.warn('Unknown message type:', msg);
          return null;
        }

        const isUser = 'query' in msg;

        return (
          <div
            key={`message-container-${index}`}
            data-message-id={msg.id}
            className={`${block}__item-wrapper w-full overflow-x-hidden break-words`}
          >
            <ChatMessage
              key={`message-${index}`}
              message={message}
              messageModel={msg}
              isUser={isUser}
              citations={citations}
              citationSources={citationSources}
              handleFeedbackDown={async () => {
                if (session?.id) {
                  await handleMessageFeedback(workbook.id, session.id, msg.id, 0, isGlobal);
                }
              }}
              handleFeedbackUp={async () => {
                if (session?.id) {
                  await handleMessageFeedback(workbook.id, session.id, msg.id, 1, isGlobal);
                }
              }}
            />
          </div>
        );
      })}

      {/* Loading indicator: Always show at the bottom when loading is true */}
      {loading && (
        <div
          className={clsx(
            `${block}__loading-indicator`,
            'flex justify-start mb-2 w-full relative overflow-hidden',
            classes.text
          )}
        >
          <div className={clsx(`${block}__loading-indicator-bubble`, 'inline-block max-w-full py-3 px-4')}>
            <div
              className={clsx(
                `${block}__loading-dots`,
                'flex items-center gap-1 text-gray-500 animate-pulse',
                classes.textMuted
              )}
            >
              <div className="h-3 w-3 bg-gray-400 rounded-full"></div>
              <div className="h-3 w-3 bg-gray-400 rounded-full animation-delay-200"></div>
              <div className="h-3 w-3 bg-gray-400 rounded-full animation-delay-400"></div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ChatMessageList;

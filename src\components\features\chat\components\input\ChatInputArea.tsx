import React, { useState, useEffect, useCallback } from 'react';
import PromptInput from './PromptInput';
import InputLowerTray from './InputLowerTray';
import { useThemeStyles } from '@/hooks/useThemeStyles';
import { ChatType } from '@/api/chatApi';
import { useFileUpload } from './fileUpload/hooks/useFileUpload';
import FilePreviewList from './fileUpload/FilePreviewList';
import { showToast } from '@/components/common/ToastNotification/ToastNotification';
import { validateFile } from './fileUpload/utils/fileUploadUtils';
import { shouldShowFileUploadUI } from './fileUpload/utils/fileUploadHelpers';

interface ChatInputAreaProps {
  promptText: string;
  handleInputChange: (e: React.ChangeEvent<HTMLTextAreaElement>) => void;
  handleSubmit: (attachedFiles?: import('@/types/fileUpload').FileInfo[]) => void;
  handleTemperatureChange: (temperature: number) => void;
  selectedChatType: ChatType;
  error?: string | null;
  isWelcomeView?: boolean;
}

const ChatInputArea: React.FC<ChatInputAreaProps> = ({
  promptText,
  handleInputChange,
  handleSubmit,
  selectedChatType,
  isWelcomeView = false,
}) => {
  const { classes, colors } = useThemeStyles();
  const block = 'chat-input-area';
  const {
    selectedFiles,
    addFiles,
    removeFile,
    getRootProps,
    getInputProps,
    isDragActive,
    openFileDialog,
    getSuccessfullyUploadedFilesInfo,
    clearUploadedFiles,
    isUploading,
  } = useFileUpload();

  const [isWindowDragging, setIsWindowDragging] = useState(false);

  const handleLocalSubmit = () => {
    if (isUploading) {
      showToast.info('Upload In Progress', 'Files are still uploading to storage. Please wait.');
      return;
    }

    const filesInfoToSend = getSuccessfullyUploadedFilesInfo();
    handleSubmit(filesInfoToSend);

    if (filesInfoToSend.length > 0) {
      clearUploadedFiles();
    }
  };

  const handleDragEnterWindow = useCallback((event: DragEvent) => {
    event.preventDefault();
    if (event.dataTransfer?.types.includes('Files')) {
      setIsWindowDragging(true);
    }
  }, []);

  const handleDragLeaveWindow = useCallback((event: DragEvent) => {
    event.preventDefault();
    if (
      event.relatedTarget === null ||
      document.elementFromPoint(event.clientX, event.clientY) === null
    ) {
      setIsWindowDragging(false);
    }
  }, []);

  const handleDropWindow = useCallback((event: DragEvent) => {
    event.preventDefault();
    setIsWindowDragging(false);
  }, []);

  const handleDragOverWindow = useCallback((event: DragEvent) => {
    event.preventDefault();
  }, []);

  const handlePaste = (event: React.ClipboardEvent<HTMLTextAreaElement>) => {
    const pastedFiles = event.clipboardData?.items;
    if (pastedFiles) {
      const files: File[] = Array.from(pastedFiles)
        .filter(item => item.kind === 'file')
        .map(item => item.getAsFile())
        .filter((file): file is File => file !== null);

      for (const file of files) {
        const fileUploadValidationResult = validateFile(file);
        if (!fileUploadValidationResult.isValid) {
          showToast.error('File Rejected', `${file.name} - ${fileUploadValidationResult?.error}`);
          continue;
        }
      }

      if (files.length > 0) {
        addFiles(files);
      }
    }
  };

  useEffect(() => {
    window.addEventListener('dragenter', handleDragEnterWindow);
    window.addEventListener('dragleave', handleDragLeaveWindow);
    window.addEventListener('drop', handleDropWindow);
    window.addEventListener('dragover', handleDragOverWindow);

    return () => {
      window.removeEventListener('dragenter', handleDragEnterWindow);
      window.removeEventListener('dragleave', handleDragLeaveWindow);
      window.removeEventListener('drop', handleDropWindow);
      window.removeEventListener('dragover', handleDragOverWindow);
    };
  }, [handleDragEnterWindow, handleDragLeaveWindow, handleDropWindow, handleDragOverWindow]);

  const containerHeightClass = isWelcomeView ? 'min-h-[160px] max-h-[350px]' : 'max-h-[300px]';
  const baseMarginBottomClass = isWelcomeView ? 'mb-2' : 'mb-4';

  let paddingAndMarginClasses = '';

  if (isWelcomeView) {
    paddingAndMarginClasses = `p-[24px] mt-2 ${baseMarginBottomClass}`;
  } else {
    paddingAndMarginClasses = `px-[24px] pt-[12px] pb-[24px] ${baseMarginBottomClass}`;
  }

  const maxWidthClass = isWelcomeView ? 'max-w-[800px]' : '';

  const showDropZoneOverlay = isWindowDragging && shouldShowFileUploadUI(selectedChatType);
  const dropZoneElementSpecificStyle = isDragActive
    ? 'border-sky-400 bg-sky-500/40'
    : 'border-sky-600 bg-sky-700/20';

  return (
    <div
      {...getRootProps({
        className: `${block} flex relative w-full ${maxWidthClass} ${containerHeightClass} ${paddingAndMarginClasses} flex-col rounded-[32px] border border-[${colors.border}] ${classes.backgroundInput} transition-all`,
        onClick: (e: React.MouseEvent) => e.stopPropagation(),
      })}
    >
      <input {...getInputProps()} />

      <div
        className={`${block}__prompt-scroll-wrapper flex-grow w-full overflow-y-auto custom-scrollbar-minimal p-1 z-10`}
      >
        <PromptInput
          message={promptText}
          onChange={handleInputChange}
          onPaste={handlePaste}
          onSubmit={handleLocalSubmit}
        />
      </div>

      {selectedFiles.length > 0 && (
        <div
          className={`${block}__file-preview-wrapper w-full flex-shrink-0 z-10 max-h-[120px] overflow-y-auto custom-scrollbar-minimal py-1 px-1`}
        >
          <FilePreviewList selectedFiles={selectedFiles} onRemoveFile={removeFile} />
        </div>
      )}

      {showDropZoneOverlay && (
        <div
          className={`${block}__drop-zone-overlay absolute inset-0 flex flex-col items-center justify-center text-center transition-colors z-20 rounded-[30px] border-4 border-dashed ${dropZoneElementSpecificStyle}`}
        >
          <p className={`${block}__drop-zone-text text-lg font-medium text-white p-4 bg-black/30 rounded-md`}>
            Drop files anywhere to add them
          </p>
        </div>
      )}

      <div className={`${block}__lower-tray-wrapper w-full flex-shrink-0 mt-auto z-10 pt-1`}>
        <InputLowerTray
          onSubmit={handleLocalSubmit}
          onAttachFileClick={openFileDialog}
          isSendingMessage={isUploading}
          disableSubmit={promptText.length === 0}
        />
      </div>
    </div>
  );
};

export default ChatInputArea;

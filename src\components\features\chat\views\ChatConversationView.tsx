import React, { useRef, useCallback } from 'react';
import { useAppDispatch, useAppSelector } from '@/store/hooks';
import {
  selectMessages,
  selectIsLoading,
  selectError,
  selectSelectedChatType,
  setMessageFeedback,
  selectFirestoreSessionId,
} from '@/store/slices/chatSlice';

import ChatMessageList from '../components/messages/ChatMessageList';
import { useChatInputHandler } from '../hooks/useChatInputHandler';
import { useAutoScroll } from '@/components/common/hooks/useAutoScroll';
import ChatInputArea from '../components/input/ChatInputArea';
import { sendFeedback } from '@/api/feedbackApi';
import { showToast } from '@/components/common/ToastNotification/ToastNotification';
import { useMessagePolling } from '../hooks/useMessagePolling';

const ChatConversationView: React.FC = () => {
  const dispatch = useAppDispatch();
  const messages = useAppSelector(selectMessages);
  const isLoading = useAppSelector(selectIsLoading);
  const error = useAppSelector(selectError);
  const selectedChatType = useAppSelector(selectSelectedChatType);
  const firestoreSessionId = useAppSelector(selectFirestoreSessionId);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const messagesContainerRef = useRef<HTMLDivElement>(null);

  const { promptText, handleInputChange, handleSubmit, handleTemperatureChange } = useChatInputHandler();

  useAutoScroll({
    messages,
    isLoading,
    messagesContainerRef,
    scrollTargetRef: messagesEndRef,
  });

  const handleFeedback = useCallback(
    async (messageId: string, feedbackValue: 0 | 1) => {
      if (!firestoreSessionId) {
        showToast.error('Feedback Failed', 'Firestore Session ID is missing. Cannot send feedback.');
        console.error('Feedback failed: Firestore Session ID is missing.');
        return;
      }

      const modelResponses = messages.filter(msg => msg.role === 'model');

      const modelResponseIndex = modelResponses.findIndex(msg => msg.id === messageId);

      if (modelResponseIndex === -1) {
        showToast.error('Feedback Failed', 'Could not find the corresponding model response to provide feedback.');
        console.error('Feedback failed: Message ID not found in model responses:', messageId);
        return;
      }

      const responseId = `${firestoreSessionId}-${modelResponseIndex}`;

      try {
        await sendFeedback(firestoreSessionId, responseId, feedbackValue);
        showToast.positive('Thank You!', 'Your feedback has been submitted successfully.');
        dispatch(setMessageFeedback({ messageId, feedback: feedbackValue }));
      } catch (err) {
        console.error(
          `Feedback submission failed (value: ${feedbackValue}, id: ${responseId}, session: ${firestoreSessionId}):`,
          err
        );
        const message = err instanceof Error ? err.message : 'Failed to submit feedback.';
        if (err instanceof Error && err.message.includes('Unexpected end of JSON input')) {
          showToast.caution('Feedback Sent', 'Feedback sent, but received an invalid response from the server.');
        } else {
          showToast.error('Feedback Failed', message);
        }
      }
    },
    [dispatch, messages, firestoreSessionId]
  );

  const handleFeedbackUp = useCallback(
    (messageId: string) => {
      handleFeedback(messageId, 1);
    },
    [handleFeedback]
  );

  const handleFeedbackDown = useCallback(
    (messageId: string) => {
      handleFeedback(messageId, 0);
    },
    [handleFeedback]
  );

  useMessagePolling();

  return (
    <div className="chat-conversation-view flex flex-col w-full h-full max-w-4xl mx-auto font-roboto text-[18px]">
      <div
        ref={messagesContainerRef}
        className="chat-conversation-view__messages-container flex-grow overflow-y-auto overflow-x-hidden pb-4 pt-4 custom-scrollbar-minimal overscroll-none dark:![&::-webkit-scrollbar-thumb]:bg-[#0066B1] w-full"
        id="chatMessagesScrollableDiv"
      >
        <ChatMessageList
          messages={messages}
          isLoading={isLoading}
          handleFeedbackUp={handleFeedbackUp}
          handleFeedbackDown={handleFeedbackDown}
        />
        <div ref={messagesEndRef} />
      </div>

      <div className="chat-conversation-view__input-area-wrapper w-full flex justify-center">
        <ChatInputArea
          promptText={promptText}
          handleInputChange={handleInputChange}
          handleSubmit={handleSubmit}
          handleTemperatureChange={handleTemperatureChange}
          selectedChatType={selectedChatType}
          error={error}
          isWelcomeView={false}
        />
      </div>
    </div>
  );
};

export default ChatConversationView;

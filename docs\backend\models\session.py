import json
from datetime import datetime, timedelta, timezone
from typing import Any, ClassVar, Collection, Dict, List, Literal, Optional, Set

from dateutil.parser import isoparse
from pydantic import (
    BaseModel,
    ConfigDict,
    Field,
    StringConstraints,
    computed_field,
    model_validator,
)
from pydantic.alias_generators import to_camel
from typing_extensions import Annotated

from models.shared.enums import ModelName, ModelTemperatures, SessionTypeEnum
from models.shared.utils import get_safe_session_type
from models.SidekickBaseModel import FileModel, SidekickBaseModel, TimestampedModel
from models.utils.partial_model import partial_model


class SessionMessage(SidekickBaseModel, TimestampedModel):
    """Prompt or Response, this will be a subcollection"""

    COLLECTION_NAME: ClassVar[str] = "SessionMessages"

    id: str
    conversation_role: Literal["user", "model"] = Field(
        ..., description="Must be 'user' or 'model'"
    )
    temperature: Annotated[
        float,
        Field(
            strict=True,
            ge=ModelTemperatures.MIN_TEMPERATURE,
            le=ModelTemperatures.MAX_TEMPERATURE,
            default=ModelTemperatures.DEFAULT_TEMPERATURE,
        ),
    ]
    message: Optional[str] = Field(default=None)
    feedback: Optional[bool] = Field(default=None)

    @model_validator(mode="after")
    def validate_message_for_user(self):
        if self.conversation_role == "user" and self.message is None:
            raise ValueError('message is required when conversation_role is "user"')
        return self

    def to_pubsub_message(
        self,
        session_id: str,
        user_email: str,
        session_type: SessionTypeEnum,
        indexed_id: str,
    ) -> dict:
        if self.conversation_role == "user":
            pubsub_message = {
                "id": indexed_id,
                "prompt_text": self.message,
                "prompt_time": self.created_utc.isoformat(),
                "prompt_user": user_email,
                "session_id": f"{session_type.name.lower()}{session_id}",
            }
        else:
            pubsub_message = {
                "prompt_id": indexed_id,
                "prompt_user": user_email,
                "response_text": self.message,
                "response_time": self.updated_utc.isoformat(),  # Using updated here because of Feedback
                "response_feedback": (
                    {"boolean": self.feedback} if self.feedback is not None else None
                ),  # Required AVRO format for nullables
                "_CHANGE_TYPE": "UPSERT",
            }
        # return json.dumps(pubsub_message).encode("utf-8")
        return pubsub_message


_createSessionMessage = partial_model(SessionMessage, "id")
CreateSessionMessage = _createSessionMessage
_updateSessionMessage = partial_model(
    SessionMessage, "temperature", "message", "conversation_role"
)
UpdateSessionMessage = _updateSessionMessage


class Prompt(BaseModel):
    """For backwards compatibility, all sessions using this will be converted to SessionMessages"""

    prompt: str
    temperature: Annotated[
        float,
        Field(
            strict=True,
            ge=ModelTemperatures.MIN_TEMPERATURE,
            le=ModelTemperatures.MAX_TEMPERATURE,
            default=ModelTemperatures.DEFAULT_TEMPERATURE,
        ),
    ]
    prompt_time_utc: datetime = Field(
        default_factory=lambda: datetime.now(timezone.utc)
    )
    response: Optional[str] = Field(default=None)
    response_time_utc: Optional[datetime] = Field(default=None)
    response_feedback: Optional[bool] = Field(default=None)

    @model_validator(mode="after")
    def generate_response_time_if_present(self):
        if self.response is not None and self.response_time_utc is None:
            self.response_time_utc = datetime.now(timezone.utc)
        return self

    def convert_to_new(self) -> List[SessionMessage]:
        """
        Convert a Prompt object to SessionMessage objects
        Returns a list of SessionMessages, max len of 2
        """
        converted_messages: List[SessionMessage] = []
        user_message = CreateSessionMessage(
            conversation_role="user",
            temperature=self.temperature,
            message=self.prompt,
            created_utc=self.prompt_time_utc,
        )
        converted_messages.append(user_message)
        if self.response is None:
            return converted_messages

        model_message = CreateSessionMessage(
            conversation_role="model",
            temperature=self.temperature,
            message=self.response,
            feedback=self.response_feedback,
            created_utc=self.response_time_utc,
        )
        converted_messages.append(model_message)

        return converted_messages


class SessionFile(SidekickBaseModel, FileModel, TimestampedModel):

    gcs_path: str
    created_utc: datetime = Field(
        default_factory=lambda: datetime.now(timezone.utc), alias="upload_time_utc"
    )

    @model_validator(mode="before")
    @classmethod
    def extract_filename(cls, data: Dict[str, Any]) -> Dict[str, Any]:
        if (
            isinstance(data, dict)
            and "gcs_path" in data
            and isinstance(data["gcs_path"], str)
        ):
            data["name"] = data["gcs_path"].split("/")[-1]

        return data

    @staticmethod
    def from_upload_dict(upload_file: dict):
        gcs_path = upload_file.get("gs_uri", upload_file.get("gcs_path", upload_file.get("gcs_uri", None)))
        if gcs_path:
            mime_type = upload_file.get("mime_type", upload_file.get("type", ""))
            file_size_bytes = upload_file.get("file_size", upload_file.get("size", 0))
            created_utc = upload_file.get("upload_time_utc", datetime.now(timezone.utc))
            if not isinstance(created_utc, datetime):
                try:
                    created_utc = isoparse(created_utc)
                except:
                    created_utc = datetime.now(timezone.utc)
            session_file = SessionFile(
                gcs_path=gcs_path,
                mime_type=mime_type,
                file_size_bytes=file_size_bytes,
                created_utc=created_utc,
            )
            return session_file
        else:
            return None


class Session(SidekickBaseModel, TimestampedModel):

    COLLECTION_NAME: ClassVar[str] = "Sessions"

    FIRESTORE_EXCLUDES: ClassVar[Dict[str, bool]] = {
        "conversation_history": True,
        "old_session_type": True,
    }
    FRONTEND_EXCLUDES: ClassVar[Dict[str, bool]] = {
        "old_session_type": True,
    }

    __EXPIRY_DELTA__: ClassVar[timedelta] = timedelta(days=10)

    id: str
    user_email: Annotated[str, StringConstraints(to_lower=True, strip_whitespace=True)]
    model: ModelName = Field(default_factory=lambda: ModelName.FLASH)
    session_type: SessionTypeEnum = Field(default_factory=get_safe_session_type)
    old_session_type: Annotated[
        Optional[str], StringConstraints(to_lower=True, pattern="chat|code|mdlm")
    ]
    session_summary: Optional[str] = Field(default=None)
    system_instructions: Annotated[Optional[str], StringConstraints(strip_whitespace=True)] = Field(default=None)
    
    file_history: List[SessionFile] = Field(default_factory=list)
    conversation_history: List[SessionMessage] = Field(default_factory=list)

    @computed_field
    @property
    def expiry_utc(self) -> datetime:
        return self.updated_utc + self.__EXPIRY_DELTA__

    @model_validator(mode="after")
    def convert_old_session_type_model(self):
        if hasattr(self, "old_session_type") and self.old_session_type:
            old_type = self.old_session_type.lower()
            if old_type == "chat":
                self.session_type = SessionTypeEnum.CHAT
            elif old_type == "code":
                self.session_type = SessionTypeEnum.CODE
            elif old_type == "mdlm":
                self.session_type = SessionTypeEnum.MDLM
        return self


_createSession = partial_model(Session, "id", "user_email", "old_session_type")
CreateSession = _createSession

# if __name__ == "__main__":
#     import os

#     os.system("clear")

#     session = CreateSession(
#         author="<EMAIL>",
#         user_email="<EMAIL>",
#         model=ModelName.FLASH,
#         session_type=SessionTypeEnum.CHAT,
#         session_summary="Example chat session",
#         system_instructions="Be helpful and concise",
#         file_history=[
#             SessionFile(
#                 gcs_path="gs://bucket/file1.txt",
#                 mime_type="text/plain",
#                 size_bytes=1024,
#             )
#         ],
#         conversation_history=[
#             CreateSessionMessage(
#                 conversation_role="user",
#                 temperature=0.7,
#                 message="Hello, how can you help me today?",
#             ),
#             CreateSessionMessage(
#                 conversation_role="model",
#                 temperature=0.7,
#                 message="I'm here to assist with your questions!",
#             ),
#         ],
#     )

#     print(json.dumps(session.to_dict(True, True, True), indent=4))

from typing import Set, Optional
from datetime import datetime, timezone
from typing_extensions import Annotated

from pydantic import BaseModel, ConfigDict, Field, StringConstraints
from pydantic.alias_generators import to_camel

class SidekickBaseModel(BaseModel):
    model_config = ConfigDict(
        alias_generator=to_camel,
        populate_by_name=True,
        from_attributes=True
    )

    def to_dict(self, date_format_iso=True, include_document_id=False, to_camel=False, to_exclude={}):
        mode = 'json' if date_format_iso else 'python'
        exclude = { **to_exclude }
        if not include_document_id:
            exclude['id'] = True

        return self.model_dump(mode=mode, exclude=exclude, by_alias=to_camel)
    
class TimestampedModel(BaseModel):
    created_utc: datetime = Field(default_factory=lambda: datetime.now(timezone.utc), alias="createdUtc")
    updated_utc: datetime = Field(default_factory=lambda: datetime.now(timezone.utc), alias="updatedUtc")
    
class TaggableModel(BaseModel):
    tags: Set[str] = Field(default_factory=set)

class UpdateTaggableModel(BaseModel):
    added_tags: Optional[Set[str]] = None
    removed_tags: Optional[Set[str]] = None

class AuthorizableModel(BaseModel):
    authorized_entities: Set[str] = Field(default_factory=set, alias="authorizedEntities")
    authorized_readers: Set[str] = Field(default_factory=set, alias="authorizedReaders")
    authorized_writers: Set[str] = Field(default_factory=set, alias="authorizedWriters")

    
    def to_dict(self, date_format_iso=True, include_document_id=False, to_camel=False, to_exclude={}):
        mode = 'json' if date_format_iso else 'python'
        exclude = { **to_exclude }
        if not include_document_id:
            exclude['id'] = True

        return self.model_dump(mode=mode, exclude=exclude, by_alias=to_camel)

class UpdateAuthorizableModel(BaseModel):
    added_authorized_entities: Optional[Set[str]] = None
    removed_authorized_entities: Optional[Set[str]] = None

    added_authorized_readers: Optional[Set[str]] = None
    removed_authorized_readers: Optional[Set[str]] = None

    added_authorized_writers: Optional[Set[str]] = None
    removed_authorized_writers: Optional[Set[str]] = None

class FileModel(BaseModel):
    name: Annotated[
        str,
        StringConstraints(
            strict=True, strip_whitespace=True, min_length=1, max_length=255
        ),
    ]

    file_size_bytes: Annotated[int, Field(strict=True, ge=0, default=0)]
    mime_type: str

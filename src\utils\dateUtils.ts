// Format date as YYYY-MM-DD
export const formatSidekickDate = (date: Date): string => {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
};

// Utility to format date as MM/DD/YYYY HH:MM:SS AM/PM
export const formatISOToReadableDateTime = (isoString: string): string => {
  try {
    const date = new Date(isoString);
    if (isNaN(date.getTime())) {
      throw new Error('Invalid date string');
    }

    const options: Intl.DateTimeFormatOptions = {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: 'numeric',
      minute: '2-digit',
      second: '2-digit',
      hour12: true,
    };
    return date.toLocaleString('en-US', options).replace(',', '');
  } catch (error) {
    console.error('Error formatting date:', isoString, error);
    return 'Invalid Date';
  }
};

// Utility to process workbook history time formatting and create title with created date if needed
export const processWorkbookHistoryTime = (createdUtc: string, updatedUtc?: string) => {
  const formattedCreatedDate = formatISOToReadableDateTime(createdUtc);
  const formattedUpdatedDate = formatISOToReadableDateTime(updatedUtc || createdUtc);

  if (formattedCreatedDate === 'Invalid Date') {
    return null; // Indicate invalid date
  }

  // Check if there's a significant difference between created and updated times
  const createdTime = new Date(createdUtc);
  const updatedTime = new Date(updatedUtc || createdUtc);
  const timeDifferenceMs = updatedTime.getTime() - createdTime.getTime();
  const timeDifferenceMinute = timeDifferenceMs / (1000 * 60);

  // If updated more than 1 minute after creation, add the created date
  const createdTitle = timeDifferenceMinute > 1 ? `(created ${formattedCreatedDate})` : '';

  return {
    title: `Session from ${formattedUpdatedDate}`,
    createdTitle,
    formattedCreatedDate,
    formattedUpdatedDate,
  };
};

import React from 'react';
import ChatMessage from './ChatMessage';
import { ChatMessageListProps } from '@/components/features/chat/chatTypes';
import { useThemeStyles } from '@/hooks/useThemeStyles';
import clsx from 'clsx';

const ChatMessageList: React.FC<ChatMessageListProps> = ({
  messages,
  isLoading,
  handleFeedbackUp,
  handleFeedbackDown,
}) => {
  const { classes } = useThemeStyles();
  const block = 'message-list';

  const extractIds = (e: React.MouseEvent): { messageId: string; sessionId: string } | null => {
    const messageElement = (e.currentTarget as HTMLElement).closest('[data-message-id]');
    const messageId = messageElement?.getAttribute('data-message-id') || '';
    const sessionId = messageId.split('-')[0] ?? '';
    if (!messageId || !sessionId) {
      console.error('Could not extract messageId or sessionId from event', e.currentTarget);
      return null;
    }
    return { messageId, sessionId };
  };

  // Wrapper handlers to extract IDs and call parent handlers
  const handleFeedbackUpWrapper = (e: React.MouseEvent) => {
    e.stopPropagation();
    const ids = extractIds(e);
    if (ids) {
      handleFeedbackUp(ids.messageId, ids.sessionId);
    }
  };

  const handleFeedbackDownWrapper = (e: React.MouseEvent) => {
    e.stopPropagation();
    const ids = extractIds(e);
    if (ids) {
      handleFeedbackDown(ids.messageId, ids.sessionId);
    }
  };

  return (
    <div className={clsx(`${block}__items-container`, 'flex flex-col space-y-3 w-full pt-4 pb-4')}>
      {messages.map(msg => (
        <div key={msg.id} data-message-id={msg.id} className={`${block}__item-wrapper`}>
          <ChatMessage
            message={{ ...msg }}
            handleFeedbackUp={handleFeedbackUpWrapper}
            handleFeedbackDown={handleFeedbackDownWrapper}
          />
        </div>
      ))}

      {/* Loading indicator: Always show at the bottom when isLoading is true */}
      {isLoading && (
        <div
          className={clsx(
            `${block}__loading-indicator`,
            'flex justify-start mb-2 w-full relative overflow-hidden',
            classes.text
          )}
        >
          <div className={clsx(`${block}__loading-indicator-bubble`, 'inline-block max-w-full py-3 px-4')}>
            <div
              className={clsx(
                `${block}__loading-dots`,
                'flex items-center gap-1 text-gray-500 animate-pulse',
                classes.textMuted
              )}
            >
              <div className="h-3 w-3 bg-gray-400 rounded-full"></div>
              <div className="h-3 w-3 bg-gray-400 rounded-full animation-delay-200"></div>
              <div className="h-3 w-3 bg-gray-400 rounded-full animation-delay-400"></div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ChatMessageList;

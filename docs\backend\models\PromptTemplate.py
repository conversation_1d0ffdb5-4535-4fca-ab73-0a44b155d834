from datetime import datetime, timezone
from typing import ClassV<PERSON>, Optional, Self, Set
from typing_extensions import Annotated

from pydantic import (
    StringConstraints,
    Field, 
    model_validator
)

from models.SidekickBaseModel import (
    SidekickBaseModel,
    TimestampedModel,
    AuthorizableModel,
    UpdateAuthorizableModel,
    TaggableModel,
    UpdateTaggableModel
)
from models.utils.partial_model import partial_model
from models.shared.enums import (
    ModelName, 
    ModelTemperatures, 
    SessionTypeEnum
)

class PromptTemplate(
    SidekickBaseModel,
    TimestampedModel,
    AuthorizableModel,
    TaggableModel
):
    """
    A `PromptTemplate` is a logical grouping of assets required for Scalable RAG

    Fields
    ------
    `created_by` : str
        same as `author` but deprecated and for backwards compatibility
    `temperature` : float
        temperature of the prompt: 0 <= temp <= 2

    Raises
    ------
    pydantic_core.ValidationError
        Like other models derived from pydantic's BaseModel, throws if public field validation fails
    """
    COLLECTION_NAME: ClassVar[str] = "PromptTemplates"

    id: str
    name: Annotated[str, StringConstraints(strict=True, strip_whitespace=True, min_length=1, max_length=255)]
    author: Annotated[str, StringConstraints(strip_whitespace=True, to_lower=True)]
    created_by: Annotated[Optional[str], StringConstraints(strip_whitespace=True, to_lower=True)] # deprecated, for backwards capability until migrated 
    prompt: Annotated[str, StringConstraints(strict=True, strip_whitespace=True, min_length=1, max_length=65536)]
    session_type: SessionTypeEnum = Field(default_factory=lambda: SessionTypeEnum.CHAT)
    model: ModelName = Field(default_factory=lambda: ModelName.FLASH)
    temperature: Annotated[float, Field(
        strict=True, 
        ge=ModelTemperatures.MIN_TEMPERATURE, 
        le=ModelTemperatures.MAX_TEMPERATURE, 
        default=ModelTemperatures.DEFAULT_TEMPERATURE
    )]
    feedback_positive: Annotated[Optional[int], Field(strict=True, ge=0, default=0)]  # Tracking upvotes - more for global prompt templates
    feedback_negative: Annotated[Optional[int], Field(strict=True, ge=0, default=0)]  # Tracking downvotes

    cloned_from: Optional[str] = Field(default=None)
    system_instructions: Optional[str] = Field(default=None)

    @model_validator(mode="before")
    @classmethod
    def patch_deprecated_fields(cls, values: dict):
        if "author" not in values:
            values["author"] = values["created_by"]
        if isinstance(values["session_type"], str):
            try:
                values["session_type"] = SessionTypeEnum[values["session_type"].upper()]
            except:
                pass
        return values
    
    @model_validator(mode="after")
    @classmethod
    def set_default_authorized_entities(cls, values: Self):
        """Sets authorized_entities to a set containing created_by if it's empty."""
        author: str = values.author if "author" in dir(values) else values.created_by
        authorized_entities: Set[str] = values.authorized_entities if "authorized_entities" in dir(values) else set()
        if author:
            if authorized_entities:
                authorized_entities.add(author)
            else:
                authorized_entities = {author}
        values.authorized_entities = authorized_entities
        return values

    def to_dict(
        self,
        date_format_iso=True,
        include_document_id=False,
        to_camel=False,
        to_exclude={},
    ):
        mode = "json" if date_format_iso else "python"
        exclude = {**to_exclude}
        if not include_document_id:
            exclude["id"] = True

        return self.model_dump(mode=mode, exclude=exclude, by_alias=to_camel)
    
CreatePromptTemplate = partial_model(PromptTemplate, 'id')

class UpdatePromptTemplate(
    SidekickBaseModel,
    UpdateAuthorizableModel,
    UpdateTaggableModel
):
    name: Annotated[Optional[str], StringConstraints(strict=True, strip_whitespace=True, min_length=1, max_length=255)] = None
    prompt: Annotated[Optional[str], StringConstraints(strip_whitespace=True, min_length=1, max_length=65536)] = None
    session_type: Optional[SessionTypeEnum] = None
    temperature: Annotated[Optional[float], Field(
        strict=True, 
        ge=ModelTemperatures.MIN_TEMPERATURE, 
        le=ModelTemperatures.MAX_TEMPERATURE, 
    )] = None

    system_instructions: Annotated[Optional[str], StringConstraints(strip_whitespace=True)] = None

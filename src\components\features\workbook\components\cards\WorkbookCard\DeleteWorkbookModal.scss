.deleteWorkbookModal__backdrop {
    position: fixed;
    inset: 0;
    z-index: var(--z-index-backdrop);
    background-color: var(--modal-backdrop-color);
    backdrop-filter: blur(var(--blur-base));
}

.deleteWorkbookModal__popup {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: var(--z-index-modal);
    display: flex;
    flex-direction: column;
    width: var(--modal-width-sm);
    color: var(--text-text-invert);
    border-radius: var(--border-radius-border-radius-xxxl);
    background: var(--elements-surface-surface-brand);
    outline: none;

    .deleteWorkbookModal__header {
        display: flex;
        align-items: flex-start;
        padding: var(--spacing-spacing-m-2) var(--spacing-spacing-m-3);
        border-bottom: var(--border-weight-border-weight-s) solid var(--button-button-primary);
        gap: var(--spacing-spacing-m-3);

        .deleteWorkbookModal__icon {
            display: flex;
            align-items: center;
            justify-content: center;
            width: var(--component-width-lg);
            height: var(--component-width-lg);
            padding: var(--spacing-spacing-m-2);
            border-radius: var(--border-radius-border-radius-xxl);
            background: var(--elements-feedback-feedback-danger);
            flex-shrink: 0;
        }

        .deleteWorkbookModal__content {
            flex: 1;
            padding-top: var(--spacing-spacing-sm-3);

            .deleteWorkbookModal__title {
                font-family: var(--font-sofia-pro);
                font-size: var(--font-size-xl);
                font-weight: var(--font-weight-bold);
                color: var(--text-text-invert);
                margin: 0 0 var(--spacing-spacing-sm-3) 0;
                white-space: nowrap;
            }

            .deleteWorkbookModal__description {
                font-family: var(--font-roboto);
                font-size: var(--font-size-sm);
                font-weight: var(--font-weight-normal);
                line-height: 1.4;
                color: var(--text-text-invert);
                margin: 0;
            }
        }
    }

    .deleteWorkbookModal__actions {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: var(--spacing-spacing-m-2) var(--spacing-spacing-m-3);
        gap: var(--spacing-spacing-m-2);

        .deleteWorkbookModal__cancel-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: var(--spacing-spacing-sm-3);
            padding: var(--spacing-spacing-sm-3) var(--spacing-spacing-m-2);
            height: var(--component-height-md);
            border-radius: var(--border-radius-border-radius-full);
            border: var(--border-weight-border-weight-s) solid var(--button-button-primary);
            background: var(--button-button-primary);
            color: var(--text-text-invert);
            font-family: var(--font-roboto);
            font-size: var(--font-size-base);
            font-weight: var(--font-weight-semibold);
            cursor: pointer;
            transition: all var(--transition-normal) ease-in-out;
            white-space: nowrap;

            &:hover {
                background: var(--button-hover-primary);
                border-color: var(--button-hover-primary);
                transform: translateY(-1px);
                box-shadow: var(--shadow-md);
            }
        }

        .deleteWorkbookModal__delete-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: var(--spacing-spacing-sm-3);
            padding: var(--spacing-spacing-sm-3) var(--spacing-spacing-m-2);
            height: var(--component-height-md);
            min-width: var(--component-width-xl);
            border-radius: var(--border-radius-border-radius-full);
            border: var(--border-weight-border-weight-s) solid var(--elements-feedback-feedback-warning);
            background: transparent;
            color: var(--elements-feedback-feedback-warning);
            font-family: var(--font-roboto);
            font-size: var(--font-size-base);
            font-weight: var(--font-weight-semibold);
            cursor: pointer;
            transition: all var(--transition-normal) ease-in-out;
            white-space: nowrap;

            &:hover:not(:disabled) {
                background: var(--elements-feedback-feedback-warning-background);
                transform: translateY(-1px);
                box-shadow: var(--shadow-md);
            }

            &--deleting,
            &:disabled {
                cursor: not-allowed;
                opacity: var(--opacity-70);
                transform: none;
                box-shadow: none;
            }
        }
    }
}

// Loading spinner animation
.deleteWorkbookModal__spinner {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

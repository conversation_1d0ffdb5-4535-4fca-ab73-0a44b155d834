import sys
from datetime import datetime, timezone
from typing import List, Optional, overload

from firebase_admin.firestore import firestore
from google.protobuf.timestamp_pb2 import Timestamp
from pydantic import ValidationError

from lib.firestore_client import (
    FirestoreClient,
    FirestoreLimitException,
    FirestoreLimits,
)
from models.Session import (
    CreateSession,
    CreateSessionMessage,
    Prompt,
    Session,
    SessionFile,
    SessionMessage,
    UpdateSessionMessage,
)
from models.shared.enums import ModelName, SessionTypeEnum


class SessionService:
    """
    Service class for managing user sessions in Firestore.

    This class provides methods to create, retrieve, and update sessions and their messages.
    It handles both current and legacy session models, providing backward compatibility.
    """

    def __init__(
        self,
        persistence_client: firestore.Client,
    ):
        """
        Initialize the SessionService with a Firestore client.

        Args:
            persistence_client (firestore.Client): The Firestore client for database operations.
        """
        self._persistence_client: firestore.Client = persistence_client

    def _get_session_by_id(
        self, session_id: str, include_messages: bool = False
    ) -> Session | None:
        """
        Retrieve a session by its ID.

        Args:
            session_id (str): The ID of the session to retrieve.
            include_messages (bool, optional): Whether to include the session's messages. Defaults to False.

        Returns:
            Session | None: The session object if found, None otherwise.

        Note:
            This method handles both current and legacy session models.
        """
        client = self._persistence_client
        session_ref = client.collection(Session.COLLECTION_NAME).document(session_id)
        session_doc = session_ref.get()
        if not session_doc.exists:
            return None
        as_dict = session_doc.to_dict()
        old_model = self._is_old_model(as_dict)
        as_dict = self._adjust_old_model(as_dict)

        if include_messages:
            if old_model:
                as_dict["conversation_history"] = self._get_old_model_messages(
                    as_dict["conversation_history"]
                )
            else:
                as_dict["conversation_history"] = self._get_messages_by_session_id(
                    session_doc.id
                )
        else:
            as_dict["conversation_history"] = []

        # Using CreateSession here as some fields are intentionally missing
        session = CreateSession(id=session_doc.id, **as_dict)
        if old_model:
            session = self._migrate_old_model(session)
        return session

    def get_user_session_by_id(
        self, session_id: str, user_email: str, include_messages: bool = False
    ) -> Session | None:
        """
        Retrieve a user's session by its ID, ensuring the session belongs to the specified user.

        Args:
            session_id (str): The ID of the session to retrieve.
            user_email (str): The email of the user who should own the session.
            include_messages (bool, optional): Whether to include the session's messages. Defaults to False.

        Returns:
            Session | None: The session object if found and owned by the user, None otherwise.
        """
        session = self._get_session_by_id(session_id, include_messages)
        if user_email and session and user_email != session.user_email:
            return None
        else:
            return session

    def get_sessions_by_user_email(
        self,
        user_email: str,
        since_date_utc: datetime = None,
        include_messages: bool = False,
    ) -> List[Session]:
        """
        Get all sessions for the given user email.

        Args:
            user_email (str): The email of the user whose sessions to retrieve.
            since_date_utc (datetime, optional): Filter sessions created on or after this date. Defaults to None.
            include_messages (bool, optional): Whether to include the sessions' messages. Defaults to False.

        Returns:
            List[Session]: A list of session objects belonging to the user, ordered by creation time (newest first).

        Note:
            This method handles both current and legacy session models.
        """
        client = self._persistence_client
        sessions_ref = client.collection(Session.COLLECTION_NAME).where(
            "user_email", "==", user_email.lower()
        )
        if since_date_utc and isinstance(since_date_utc, datetime):
            sessions_ref = sessions_ref.where("created_utc", ">=", since_date_utc)
        query = sessions_ref.order_by(
            "created_utc", direction=firestore.Query.DESCENDING
        )
        session_docs = query.stream()
        sessions: List[Session] = []
        # google.api_core.exceptions.FailedPrecondition: 400 The query requires an index.
        for session_doc in session_docs:
            if session_doc.exists:
                as_dict: dict = session_doc.to_dict()
                old_model = self._is_old_model(as_dict)
                as_dict = self._adjust_old_model(as_dict)
                if include_messages:
                    if old_model:
                        as_dict["conversation_history"] = self._get_old_model_messages(
                            as_dict["conversation_history"]
                        )
                    else:
                        as_dict["conversation_history"] = (
                            self._get_messages_by_session_id(session_doc.id)
                        )

                else:
                    as_dict["conversation_history"] = []

                try:
                    session = CreateSession(id=session_doc.id, **as_dict)
                    sessions.append(session)
                except ValidationError as e:
                    pass  # Skip sessions that cannot be converted due to errors
        return sessions

    def get_message_by_id(
        self, session_id: str, message_id: str
    ) -> SessionMessage | None:
        """
        Get a specific message from a session.

        Args:
            session_id (str): The ID of the session containing the message.
            message_id (str): The ID of the message to retrieve.

        Returns:
            SessionMessage | None: The message object if found, None otherwise.
        """
        client = self._persistence_client
        session_ref = client.collection(Session.COLLECTION_NAME).document(session_id)
        message_ref: firestore.CollectionReference = session_ref.collection(
            SessionMessage.COLLECTION_NAME
        )
        message_doc = message_ref.document(message_id).get()
        if message_doc.exists:
            as_dict = message_doc.to_dict()
            message = SessionMessage(id=message_doc.id, **as_dict)
            return message
        return None

    def create_new_session(
        self,
        session: CreateSession,
    ) -> Session:
        """
        Create a new session in Firestore.

        Args:
            session (CreateSession): The session object to create.

        Returns:
            Session: The created session object with its assigned ID.

        """

        session_ref: firestore.CollectionReference = (
            self._persistence_client.collection(Session.COLLECTION_NAME)
        )
        as_dict = session.to_dict(
            date_format_iso=False, to_exclude=Session.FIRESTORE_EXCLUDES
        )
        as_dict["session_type"] = as_dict["session_type"].value
        created_at, session_ref = session_ref.add(
            document_data=as_dict, document_id=session.id
        )

        to_return = Session(id=session_ref.id, **session.to_dict(date_format_iso=False))
        return to_return

    def add_message(
        self,
        session_id: str,
        message: CreateSessionMessage,
        migration: bool = False,
    ) -> SessionMessage:
        """
        Add a new message to an existing session.

        Args:
            session_id (str): The ID of the session to add the message to.
            message (CreateSessionMessage): The message object to add.
            migration (bool, optional): Whether this method is being called during a migration. Defaults to False.

        Returns:
            SessionMessage: The created message object with its assigned ID.
        """

        session_ref: firestore.DocumentReference = self._persistence_client.collection(
            Session.COLLECTION_NAME
        ).document(session_id)
        messages_ref: firestore.CollectionReference = session_ref.collection(
            SessionMessage.COLLECTION_NAME
        )

        session_update = {}
        if message.conversation_role == "user":
            session_update.update({"session_summary": message.message})

        if not migration:
            session_update.update({"updated_utc": message.updated_utc})
        if session_update:
            session_ref.update(session_update)

        as_dict = message.to_dict(date_format_iso=False)
        created_at, message_ref = messages_ref.add(
            document_data=as_dict, document_id=message.id
        )
        message_doc = SessionMessage(id=message_ref.id, **as_dict)
        return message_doc

    # def add_response(
    #     self, session_id: str, response: CreateSessionMessage, prompt_id: str
    # ) -> SessionMessage:
    #     """Keeps prompts & response id similar"""
    #     response.id = f"{prompt_id}-response"

    #     return self.add_message(session_id=session_id, message=response)

    def update_message_feedback(
        self,
        session_id: str,
        update_message: UpdateSessionMessage,
    ) -> UpdateSessionMessage:
        """
        Update an existing message in a session.

        Args:
            session_id (str): The ID of the session containing the message.
            update_message (SessionMessage): The message object with updated data.

        Returns:
            UpdateSessionMessage: The updated message object.

        """

        sessions_ref: firestore.CollectionReference = (
            self._persistence_client.collection(Session.COLLECTION_NAME)
        )
        messages_ref: firestore.CollectionReference = sessions_ref.document(
            session_id
        ).collection(SessionMessage.COLLECTION_NAME)

        as_dict = update_message.to_dict(date_format_iso=False)
        write_result = messages_ref.document(update_message.id).update(
            {"feedback": as_dict["feedback"], "updated_utc": as_dict["updated_utc"]}
        )
        message_doc = UpdateSessionMessage(id=update_message.id, **as_dict)

        return message_doc

    def add_files(self, session: Session, files: List[SessionFile]) -> Session:
        session_ref: firestore.DocumentReference = self._persistence_client.collection(
            Session.COLLECTION_NAME
        ).document(session.id)

        session.file_history.extend(files)
        as_dict = session.to_dict(
            date_format_iso=False, to_exclude=Session.FIRESTORE_EXCLUDES
        )
        as_dict["session_type"] = as_dict["session_type"].value

        session_ref.update(as_dict)

        return session

    def _migrate_old_model(self, session: Session) -> Session | None:
        """
        Convert legacy session model data to the current model format.

        Args:
            session (str): The old model session_id converted to the new format.

        Returns:
            Session: A session converted to the new model format.
        """
        client = self._persistence_client
        session_ref = client.collection(Session.COLLECTION_NAME).document(session.id)
        session_doc = session_ref.get()
        if not session_doc.exists:
            return None

        session.updated_utc = session.created_utc
        for files in session.file_history:
            files.updated_utc = files.created_utc

        as_dict = session.to_dict(
            date_format_iso=False, to_exclude=Session.FIRESTORE_EXCLUDES
        )
        as_dict["session_type"] = as_dict["session_type"].value
        as_dict.update({"conversation_history": firestore.DELETE_FIELD})
        session_ref.update(as_dict)

        # Gets the IDs of the newly migrated messages
        new_conversation_history: List[SessionMessage] = []
        for message in session.conversation_history:
            message.updated_utc = message.created_utc
            added_message = self.add_message(session.id, message, migration=True)
            new_conversation_history.append(added_message)

        session.conversation_history = new_conversation_history
        session.session_summary = session.conversation_history[-1].message
        return session

    @staticmethod
    def _get_old_model_messages(old_conversation_history: list) -> List[SessionMessage]:
        """
        Convert messages from the old model format to the new SessionMessage format.

        Args:
            old_conversation_history (list): List of conversation history items in the old format.

        Returns:
            List[SessionMessage]: List of converted SessionMessage objects.
        """
        new_conversation_history: List[SessionMessage] = []
        for conversation_history in old_conversation_history:
            new_conversation_history += Prompt(**conversation_history).convert_to_new()
        return new_conversation_history

    def _get_messages_by_session_id(self, session_id: str) -> List[SessionMessage]:
        """
        Retrieve all messages for a given session, ordered by creation time.

        Args:
            session_id (str): The ID of the session whose messages to retrieve.

        Returns:
            List[SessionMessage]: List of SessionMessage objects belonging to the session.
        """
        client = self._persistence_client
        session_ref = client.collection(Session.COLLECTION_NAME).document(session_id)
        messages_ref: firestore.CollectionReference = session_ref.collection(
            SessionMessage.COLLECTION_NAME
        )
        message_docs = messages_ref.order_by("created_utc").stream()

        conversation_history = []
        for doc in message_docs:
            conversation_history.append(SessionMessage(id=doc.id, **doc.to_dict()))
        return conversation_history

    @staticmethod
    def _is_old_model(as_dict: dict) -> bool:
        """
        Determine if a session dictionary represents an old model format.

        This checks the presence of 'conversation_history' and the value type of 'session_type'.

        Args:
            as_dict (dict): The session dictionary to check.

        Returns:
            bool: True if the session is in the old model format, False otherwise.
        """
        return (
            not isinstance(as_dict.get("session_type"), SessionTypeEnum)
            and len(as_dict.get("conversation_history", [])) > 0
        )

    @staticmethod
    def _adjust_old_model(as_dict: dict) -> dict:
        """
        Adjust an old model session dictionary to be compatible with the new model.

        Args:
            as_dict (dict): The session dictionary to adjust.

        Returns:
            dict: The adjusted session dictionary.
        """
        if SessionService._is_old_model(as_dict):
            as_dict.update(
                {
                    "old_session_type": as_dict["session_type"],
                    "session_type": SessionTypeEnum.MDLM,  # Will be corrected via validator
                    "session_summary": as_dict["conversation_history"][-1]["prompt"],
                    "updated_utc": as_dict["created_utc"],
                }
            )
        return as_dict
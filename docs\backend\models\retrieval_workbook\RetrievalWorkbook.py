from typing import Optional, Set, List, ClassVar

from datetime import datetime, timezone
from pydantic import Field, StringConstraints
from typing_extensions import Annotated

from models.shared.enums import ModelTemperatures
from models.SidekickBaseModel import (
    SidekickBaseModel,
    TimestampedModel,
    AuthorizableModel,
    UpdateAuthorizableModel,
    TaggableModel,
    UpdateTaggableModel
)

from .RetrievalEnums import RetrievalWorkbookChunkSize
from .RetrievalSession import RetrievalSession
from .RetrievalFile import RetrievalFile, RetrievalFileSignedUrl
from ..utils.partial_model import partial_model

class RetrievalWorkbook(
    SidekickBaseModel,
    TimestampedModel,
    AuthorizableModel,
    TaggableModel
):
    """
    A `RetrievalWorkbook` is a logical grouping of assets required for Scalable RAG

    Fields
    ------
    `id` : str
    `name` : str
    `author` : str
        email address of the user that created the `RetrievalWorkbook`
    `data_store_id` : str
        id of the Vertex AI Search (VAIS) vector database this workbook uses
    `description` : Optional[str]
    `cloned_from` : Optional[str]
        id of another `RetrievalWorkbook` that this workbook is cloned from
    `chunk_size` : RetrievalWorkbookChunkSize
        size of the chunks generated by VAIS' chunking process when ingesting a file
    `files` : List[RetrievalFile]
        array of uploaded `RetrievalFile`s that are associated with this workbook
    `sessions` : List[RetrievalSession]
        array of `RetrievalSession`s that are associated with this workbook

    Raises
    ------
    pydantic_core.ValidationError
        Like other models derived from pydantic's BaseModel, throws if public field validation fails
    """
    COLLECTION_NAME: ClassVar[str] = "RetrievalWorkbooks"
    
    id: str
    name: Annotated[str, StringConstraints(strict=True, strip_whitespace=True, min_length=1, max_length=255)]
    author: Annotated[str, StringConstraints(strip_whitespace=True, to_lower=True)]
    search_app_id: str
    data_store_id: str
    description: Annotated[Optional[str], StringConstraints(strip_whitespace=True, min_length=0, max_length=1024)] = None
    cloned_from: Annotated[Optional[str], StringConstraints(strip_whitespace=True, to_lower=True)] = None
    chunk_size: RetrievalWorkbookChunkSize = Field(default_factory=lambda: RetrievalWorkbookChunkSize.large)
    temperature: Annotated[
        float,
        Field(
            strict=True,
            ge=ModelTemperatures.MIN_TEMPERATURE,
            le=ModelTemperatures.MAX_TEMPERATURE,
            default=ModelTemperatures.MIN_TEMPERATURE,
        ),
    ]
    system_instructions: Annotated[Optional[str], StringConstraints(strip_whitespace=True)] = None

    files: List[RetrievalFile] = Field(default_factory=list)
    sessions: List[RetrievalSession] = Field(default_factory=list)

    def to_dict(self, date_format_iso=True, include_document_id=False, to_camel=False, to_exclude={}):
        mode = 'json' if date_format_iso else 'python'
        exclude = { **to_exclude }
        if not include_document_id:
            exclude['id'] = True

        return self.model_dump(mode=mode, exclude=exclude, by_alias=to_camel)

CreateRetrievalWorkbook = partial_model(RetrievalWorkbook, 'id', 'search_app_id', 'data_store_id')

class UpdateRetrievalWorkbook(
    SidekickBaseModel,
    UpdateAuthorizableModel,
    UpdateTaggableModel
):
    name: Annotated[Optional[str], StringConstraints(strict=True, strip_whitespace=True, min_length=1, max_length=255)] = None
    description: Annotated[Optional[str], StringConstraints(strip_whitespace=True, min_length=0, max_length=1024)] = None
    clear_description: bool = False
    system_instructions: Annotated[Optional[str], StringConstraints(strip_whitespace=True)] = None
    temperature: Annotated[Optional[float], Field(
        strict=True, 
        ge=ModelTemperatures.MIN_TEMPERATURE, 
        le=ModelTemperatures.MAX_TEMPERATURE,
        default=None
    )] = None

class RetrievalWorkbookSummary(RetrievalWorkbook):
    num_files: Annotated[int, Field(strict=True, ge=0, default=0)]

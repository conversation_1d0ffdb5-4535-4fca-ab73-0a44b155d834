import traceback
from enum import IntEnum
from typing import Dict, List, MutableSequence, Tu<PERSON>, Union

from google.api_core.client_options import ClientOptions
from google.cloud import discoveryengine_v1 as discoveryengine
from google.cloud.discoveryengine_v1 import (
    FactChunk,
    GenerateGroundedContentRequest,
    GenerateGroundedContentResponse,
    GroundedGenerationContent,
    GroundedGenerationServiceClient,
    GroundingFact,
)
from google.cloud.exceptions import NotFound as GCSNotFound

from models.retrieval_workbook.RetrievalEnums import RetrievalWorkbookChunkSize
from models.shared.enums import ModelName, ModelTemperatures
from website.config import get_config_val
from website.logger_framework import make_logger
from website.extensions import oidc_variables
from website.google_cloud_init import fetch_ENV_by_project_id

workflow = "vais_service"
logger_info, logger_error = make_logger(workflow, __file__)

class VertexAISearchService:
    # google.api_core.exceptions.InvalidArgument: 400 Invalid filter syntax
    SYSTEM_INSTRUCTION = "Use grounded sources whenever possible."

    def __init__(
        self,
        domain: str,
        chunk_size: RetrievalWorkbookChunkSize | int = RetrievalWorkbookChunkSize.large,
        client: GroundedGenerationServiceClient | None = None,
    ):
        self.project_id = oidc_variables.get(
            "cloudrun_project_id", "gcp-genai-3-d-services-dciw"
        )  # might need moved to argument depending on when oidc gets init

        self.project_environment = fetch_ENV_by_project_id(self.project_id)
        self.project_number = get_config_val("project_number", self.project_environment)

        retrieval_workbooks: dict = get_config_val(
            "retrieval_workbooks", self.project_environment
        )
        (self.data_store_id, self.search_app_id) = (
            VertexAISearchService.get_search_config(
                domain, chunk_size, retrieval_workbooks
            )
        )

        self.model_id = ModelName.FLASH.value
        self.location = "us"
        self.client = client if client else GroundedGenerationServiceClient()
        document_service_client_options = ClientOptions(
            api_endpoint=(
                f"{self.location}-discoveryengine.googleapis.com"
                if self.location != "global"
                else None
            )
        )
        self.document_service_client = discoveryengine.DocumentServiceClient(
            client_options=document_service_client_options
        )
        self.common_location_path = self.client.common_location_path(
            project=self.project_number, location=self.location
        )

        pass

    @staticmethod
    def make_search_filter(
        domain: str,
        workbook_id: str,
    ) -> str:
        search_filter = f'domain: ANY("{domain}") AND workbook_id: ANY("{workbook_id}")'
        # search_filter = f'workbook_id:ANY("{workbook_id}")'

        return search_filter

    @staticmethod
    def get_search_config(
        domain: str, chunk_size: RetrievalWorkbookChunkSize | int, search_config: dict
    ):
        """Read from provided config & fetch data_store_id & search_app_id

        Args:
            domain (str): Email domain email.split('@')[1]
            chunk_size (RetrievalWorkbookChunkSize | int): RetrievalWorkbookChunkSize
            search_config (dict): Provided config {"domain_to_data_stores":"","domain_to_search_apps":""}

        Returns:
            Tuple: (data_store_id, search_app_id)
        """

        if isinstance(chunk_size, int):
            try:
                chunk_size = RetrievalWorkbookChunkSize(chunk_size)
            except ValueError:
                chunk_size = RetrievalWorkbookChunkSize.large

        domain_to_data_stores: dict = search_config.get("domain_to_data_stores", {})
        domain_to_search_apps: dict = search_config.get("domain_to_search_apps", {})

        default_data_store: dict = domain_to_data_stores.get("default")
        data_store_config: dict = domain_to_data_stores.get(domain, default_data_store)

        default_search_app: dict = domain_to_search_apps.get("default")
        search_app_config: dict = domain_to_search_apps.get(domain, default_search_app)

        data_store_id: str = data_store_config.get(
            str(chunk_size.value), str(RetrievalWorkbookChunkSize.large.value)
        )
        search_app_id: str = search_app_config.get(
            str(chunk_size.value), str(RetrievalWorkbookChunkSize.large.value)
        )

        return (data_store_id, search_app_id)

    @staticmethod
    def generate_document(document_id, metadata, content) -> discoveryengine.Document:
        """
        Ex: id="doc-999"
            struct_data={"title": "", "description": "", "user": "", "notebook": ""}
            content={"mime_type": "application/pdf", "uri": ""}
        """
        document = discoveryengine.Document(
            id=document_id, struct_data=metadata, content=content
        )
        return document

    def import_documents_to_datastore(
        self, data_store_id: str, documents: List[discoveryengine.Document]
    ):
        try:
            parent = self.document_service_client.branch_path(
                project=self.project_id,
                location=self.location,
                data_store=data_store_id,
                branch="default_branch",
            )

            document_source = discoveryengine.ImportDocumentsRequest.InlineSource(
                documents=documents
            )
            import_request = discoveryengine.ImportDocumentsRequest(
                parent=parent,
                inline_source=document_source,
                reconciliation_mode=discoveryengine.ImportDocumentsRequest.ReconciliationMode.INCREMENTAL,
            )

            import_operation = self.document_service_client.import_documents(
                request=import_request
            )
            import_operation.result()
        except Exception as error:
                logger_error.error(f"{workflow} : import_documents : {str(error)} : {traceback.format_exc()}")
                raise

    def get_document_index_status(self, document_id) -> bool:
        document_path = f"projects/{self.project_id}/locations/{self.location}/dataStores/{self.data_store_id}/branches/default_branch/documents/{document_id}"
        get_document_request = discoveryengine.GetDocumentRequest(
            name=document_path,
        )
        
        try:
            get_document_response = self.document_service_client.get_document(
                request=get_document_request
            )
            pending_message = get_document_response.index_status.pending_message
            logger_info.info(f"{workflow} : document_index_status : {pending_message}")

            return get_document_response.index_status.index_time is not None
        except GCSNotFound as nfe:
            logger_error.error(f"{workflow} : import_documents : {str(nfe)} : {traceback.format_exc()}")
            raise
        except Exception as e:
            logger_error.error(f"{workflow} : import_documents : {str(e)} : {traceback.format_exc()}")
            raise

    def delete_document_index(self, document_id) -> bool:
        document_path = f"projects/{self.project_id}/locations/{self.location}/dataStores/{self.data_store_id}/branches/default_branch/documents/{document_id}"
        delete_document_request = discoveryengine.DeleteDocumentRequest(
            name=document_path
        )

        try:
            self.document_service_client.delete_document(
                request=delete_document_request
            )
            return True
        except GCSNotFound as nfe:
            return True
        except Exception as e:
            logger_error.error(f"{workflow} : delete_document : {str(e)} : {traceback.format_exc()}")
            raise

    def _make_grounded_request(
        self,
        search_filter: str,
        prompt: str,
        system_instructions: str = None,
        temperature: float = ModelTemperatures.MIN_TEMPERATURE,
        extra_contents: List[GroundedGenerationContent] = [],
    ):
        """Form grounded request"""

        system_instruction_parts = [self.SYSTEM_INSTRUCTION, system_instructions]
        effective_system_instructions = "\n\n".join([sip for sip in system_instruction_parts if sip is not None])
        system_instruction = GroundedGenerationContent(
            parts=[GroundedGenerationContent.Part(text=effective_system_instructions)],
        )
        contents = [self.form_content("user", prompt)]
        contents = extra_contents + contents
        grounding_spec = GenerateGroundedContentRequest.GroundingSpec(
            grounding_sources=[
                GenerateGroundedContentRequest.GroundingSource(
                    search_source=GenerateGroundedContentRequest.GroundingSource.SearchSource(
                        serving_config=f"projects/{self.project_number}/locations/us/collections/default_collection/engines/{self.search_app_id}/servingConfigs/default_search",
                        filter=search_filter,
                    ),
                ),
            ]
        )

        request = GenerateGroundedContentRequest(
            location=self.common_location_path,
            generation_spec=GenerateGroundedContentRequest.GenerationSpec(
                model_id=self.model_id,
                temperature=temperature,
            ),
            system_instruction=system_instruction,
            contents=contents,
            # What to ground on.
            grounding_spec=grounding_spec,
        )

        return request

    @staticmethod
    def form_content(role: str, message: str) -> GroundedGenerationContent:
        return GroundedGenerationContent(
            role=role,
            parts=[GroundedGenerationContent.Part(text=message)],
        )

    @staticmethod
    def get_candidate_data(
        response_candidates: GenerateGroundedContentResponse,
    ) -> Dict[
        str,
        Union[
            str,
            MutableSequence[
                GenerateGroundedContentResponse.Candidate.GroundingMetadata.GroundingSupport
            ],
            MutableSequence[FactChunk],
        ],
    ]:
        """Uses response candidates, get grounding_metadata if any."""
        candidates = response_candidates.candidates
        if len(candidates) == 0:
            # Maybe raise an error?
            return {
                "response_text": "",
                "grounding_supports": [],
                "support_chunks": [],
            }

        response_text = candidates[0].content.parts[0].text
        grounding_supports = candidates[0].grounding_metadata.grounding_support
        support_chunks = candidates[0].grounding_metadata.support_chunks
        return {
            "response_text": response_text,
            "grounding_supports": grounding_supports,
            "support_chunks": support_chunks,
        }

    def send_grounded_request(
        self,
        search_filter: str,
        prompt: str,
        system_instructions: str = None,
        temperature: float = ModelTemperatures.MIN_TEMPERATURE,
        extra_contents: List[GroundedGenerationContent] = [],
    ):
        request = self._make_grounded_request(search_filter, prompt, 
            extra_contents=extra_contents,
            system_instructions=system_instructions,
            temperature=temperature
        )
        response = self.client.generate_grounded_content(request)
        return response


# if __name__ == "__main__":

#     vais = VertexAISearchService("highmarkhealth.org", RetrievalWorkbookChunkSize.large)

#     search_filter = {
#         # "user": "<EMAIL>",
#         "workbook_id": "dxjah39Qdhuk3jBk8PwB",
#         # "description": "idk some session stuff", # Doesn't seem to be searchable
#         # "title": "Session.txt", # Doesn't seem to be searchable
#     }

#     response = vais.send_grounded_request(
#         search_filter=vais.make_search_filter(**search_filter),
#         prompt="How many classmethods have I used in all the files provided?",
#     )
#     candidate_data = vais.get_candidate_data(response)
#     formatted_response = vais.format_response(**candidate_data)
#     print(candidate_data)
#     # print(formatted_response)
#     # returns 1-2 references
#     pass

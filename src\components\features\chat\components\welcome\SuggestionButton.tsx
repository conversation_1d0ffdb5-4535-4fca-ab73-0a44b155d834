import React from 'react';
import { useThemeStyles } from '@hooks/useThemeStyles';
import { SuggestionButtonProps } from '@/types/layout';

const SuggestionButton: React.FC<SuggestionButtonProps> = ({ icon, label, onClick }) => {
  const { classes, colors } = useThemeStyles();

  return (
    <button
      className={`suggestion-button flex items-center gap-2 px-4 py-2 rounded-full border border-[${colors.border}] ${classes.text} ${classes.hoverBackground} transition-colors font-roboto`}
      onClick={onClick}
      tabIndex={0}
      aria-label={label}
    >
      <span className="suggestion-button__icon">{icon}</span>
      <span className="suggestion-button__label text-sm font-medium font-roboto">{label}</span>
    </button>
  );
};

export default SuggestionButton;

/**
 * Utility functions for workbook-related operations
 */

/**
 * Build workbook history path
 */
export const buildWorkbookHistoryPath = (workbookId: string, isGlobal: boolean): string => {
  return isGlobal ? `/workbooks/public/${workbookId}/sessions` : `/workbooks/my/${workbookId}/sessions`;
};

/**
 * Build workbook resume session path
 */
export const buildWorkbookResumeSessionPath = (workbookId: string, sessionId: string, isGlobal: boolean): string => {
  return `${isGlobal ? '/workbooks/public' : '/workbooks/my'}/${workbookId}/sessions/${sessionId}`;
};

/**
 * Build workbook base path
 */
export const buildWorkbookBasePath = (workbookId: string, isGlobal: boolean): string => {
  return isGlobal ? `/workbooks/public/${workbookId}` : `/workbooks/my/${workbookId}`;
};

/**
 * Extract latest message from session messages
 */
export const extractLatestMessage = (messages?: any[]): string => {
  if (!messages || messages.length === 0) {
    return '[No messages]';
  }

  const firstMessage = messages[0];
  if (firstMessage && firstMessage.query) {
    return firstMessage.query;
  }

  return '[No messages]';
};

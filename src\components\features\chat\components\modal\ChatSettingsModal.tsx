import React, { useState, useEffect } from 'react';
import { IoClose } from 'react-icons/io5';
import { MdOutlineSave } from 'react-icons/md';
import { Dialog } from '@base-ui-components/react/dialog';
import { Radio } from '@base-ui-components/react/radio';
import { RadioGroup } from '@base-ui-components/react/radio-group';
import '../../../workbook/components/dashboard/SettingsModal.scss';

interface ChatSettingsModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (temperature: number) => void;
  currentTemperature: number;
  isLoading?: boolean;
}

type TemperatureId = 'less-creative' | 'classic' | 'more-creative';

// Chat temperature configuration - aligned with chat system values
const chatTemperatureOptions: { id: TemperatureId; value: number; displayName: string }[] = [
  { id: 'less-creative', value: 0.2, displayName: 'Less Creative' },
  { id: 'classic', value: 1.0, displayName: 'Classic' },
  { id: 'more-creative', value: 1.8, displayName: 'More Creative' },
];

const defaultChatTemperatureOption = chatTemperatureOptions[1]; // 'classic'

// Temperature conversion functions for chat
export const chatTemperatureValueToOption = (value: number): TemperatureId => {
  const option = chatTemperatureOptions.find(opt => opt.value === value);
  return option ? option.id : defaultChatTemperatureOption.id;
};

export const chatTemperatureOptionToValue = (optionId: TemperatureId): number => {
  const option = chatTemperatureOptions.find(opt => opt.id === optionId);
  return option ? option.value : defaultChatTemperatureOption.value;
};

export const getChatTemperatureDisplayName = (value: number): string => {
  const option = chatTemperatureOptions.find(opt => opt.value === value);
  return option ? option.displayName : defaultChatTemperatureOption.displayName;
};

const ChatSettingsModal: React.FC<ChatSettingsModalProps> = ({
  isOpen,
  onClose,
  onSave,
  currentTemperature,
  isLoading = false,
}) => {
  const [selectedTemperature, setSelectedTemperature] = useState<TemperatureId>('classic');
  const [isSaving, setIsSaving] = useState(false);

  // Initialize form with current temperature when modal opens
  useEffect(() => {
    if (isOpen) {
      setSelectedTemperature(chatTemperatureValueToOption(currentTemperature));
    }
  }, [isOpen, currentTemperature]);

  const handleSave = async () => {
    if (isSaving) return;

    setIsSaving(true);
    try {
      const temperatureValue = chatTemperatureOptionToValue(selectedTemperature);
      onSave(temperatureValue);
      onClose();
    } catch (error) {
      console.error('Failed to save chat settings:', error);
    } finally {
      setIsSaving(false);
    }
  };

  const handleCancel = () => {
    // Reset to current temperature
    setSelectedTemperature(chatTemperatureValueToOption(currentTemperature));
    onClose();
  };

  return (
    <Dialog.Root open={isOpen} onOpenChange={onClose}>
      <Dialog.Portal>
        <Dialog.Backdrop className="settings-modal-overlay" />
        <Dialog.Popup className="settings-modal">
          {/* Close Button */}
          <Dialog.Close className="settings-modal__close-btn" aria-label="Close modal">
            <IoClose className="w-6 h-6" />
          </Dialog.Close>

          {/* Content */}
          <div className="settings-modal__content">
            {/* Title */}
            <Dialog.Title className="settings-modal__title">Chat Settings</Dialog.Title>

            {/* Temperature Section */}
            <div className="settings-modal__temperature-section">
              <h3 className="settings-modal__temperature-title">Temperature</h3>
              <p className="settings-modal__temperature-description">
                Controls the creativity and randomness of responses
              </p>

              {/* Radio Button Group */}
              <div className="settings-modal__radio-group">
                <RadioGroup
                  value={selectedTemperature}
                  onValueChange={value => setSelectedTemperature(value as TemperatureId)}
                  className="flex items-center gap-6"
                >
                  {chatTemperatureOptions.map(option => (
                    <label key={option.id} className="settings-modal__radio-option">
                      <Radio.Root value={option.id} className="settings-modal__radio-input">
                        <Radio.Indicator className="settings-modal__radio-indicator" />
                      </Radio.Root>
                      <span>{option.displayName}</span>
                    </label>
                  ))}
                </RadioGroup>
              </div>
            </div>

            {/* Buttons */}
            <div className="settings-modal__actions">
              <button onClick={handleCancel} className="settings-modal__button settings-modal__button--cancel">
                Cancel
              </button>
              <button
                onClick={handleSave}
                disabled={isSaving || isLoading}
                className="settings-modal__button settings-modal__button--save"
              >
                <MdOutlineSave className="w-5 h-5" />
                <span>{isSaving ? 'Saving...' : 'Save Settings'}</span>
              </button>
            </div>
          </div>
        </Dialog.Popup>
      </Dialog.Portal>
    </Dialog.Root>
  );
};

export default ChatSettingsModal;

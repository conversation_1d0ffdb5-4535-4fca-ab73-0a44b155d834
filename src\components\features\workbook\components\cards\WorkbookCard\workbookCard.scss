.workbook-title {
    word-break: break-word;
}

.workbook-card {
    width: var(--workbook-card-width);
    height: var(--workbook-card-height);
    border-radius: var(--border-radius-border-radius-xl);
    border: var(--border-weight-border-weight-s) solid var(--border-color);
    background: var(--background-color);
    overflow: hidden;
    transition: transform var(--transition-normal) ease-in-out;
    padding: var(--spacing-spacing-m-3);
    display: flex;
    flex-direction: column;

    &:hover {
        transform: scale(calc(1 + 0.05 * (1 - var(--disable-card-hover, 0))));
    }
}

.workbook-card__content {
    height: 100%;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-spacing-m-2);
}

.workbook-card__header {
    display: flex;
    flex-direction: column;
    flex-grow: 1;
    justify-content: space-between;
    gap: var(--spacing-spacing-m-2);
    min-height: 0;
}

.workbook-card__title {
    font-family: var(--font-sofia-pro);
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-bold);
    line-height: 1.2;
    color: var(--text-text-invert);
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    word-break: break-word;
}

.workbook-card__footer {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-spacing-m-2);
}

.workbook-card__footer-info {
    // This class is now just a container for the date and divider. No special styling needed.
}

.workbook-card__date {
    font-family: var(--font-roboto);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-normal);
    line-height: 1.0;
    color: var(--text-text-invert);
    margin-bottom: var(--spacing-spacing-m-1);
}

.workbook-card__divider {
    width: 100%;
    height: var(--border-weight-border-weight-s);
    background-color: var(--button-button-primary);
}

.workbook-card__run-button {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-spacing-sm-2);
    padding: var(--spacing-spacing-sm-1) var(--spacing-spacing-sm-4);
    border-radius: var(--border-radius-border-radius-full);
    border: var(--border-weight-border-weight-s) solid var(--button-button-primary);
    background-color: transparent;
    cursor: pointer;
    font-family: var(--font-roboto);
    font-size: var(--font-size-base);
    color: var(--text-text-invert);
    transition: background-color var(--transition-slow), color var(--transition-slow);
    align-self: flex-start;

    &:hover {
        background-color: var(--button-button-primary);
        color: var(--text-text-invert);
    }
}

.workbook-card__manage-button {
    display: flex;
    width: var(--workbook-manage-width);
    padding: var(--spacing-spacing-sm-2) var(--spacing-spacing-sm-3);
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-spacing-m-1);
    border-radius: var(--border-radius-border-radius-l);
    border: var(--border-weight-border-weight-s) solid var(--button-button-primary);
    background: var(--elements-surface-surface-brand);
    color: var(--text-text-invert);
    cursor: pointer;
    font-family: var(--font-roboto);
    transition: transform var(--transition-normal) ease, box-shadow var(--transition-normal) ease;

    &:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-lg);
    }
}

.workbook-card__manage-content {
    display: flex;
    padding: var(--spacing-spacing-m-1) var(--spacing-spacing-m-3);
    align-items: center;
    gap: var(--spacing-spacing-sm-3);
    align-self: stretch;
    border-radius: var(--border-radius-border-radius-m) var(--border-radius-border-radius-m) 0px 0px;
    opacity: var(--opacity-70);
    font-size: var(--font-size-base);
}

.workbook-card__manage-popup {
    background: var(--modal-hover-background);
    width: var(--workbook-manage-width); /* Same as manage button */
    padding: var(--spacing-spacing-m-1) 0px 0px var(--spacing-spacing-m-1);
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-spacing-sm-2);
    align-self: stretch;
    border-radius: var(--border-radius-border-radius-l);
    border: var(--border-weight-border-weight-s) solid var(--button-button-primary);
    backdrop-filter: blur(var(--blur-lg));
    z-index: var(--z-index-dropdown);
    box-shadow: var(--shadow-md);
    overflow: hidden; /* This prevents shadows from extending beyond menu boundaries */
    
    /* Animation for smooth appearance */
    opacity: 0;
    transform: translateY(var(--spacing-spacing-sm-3));
    transition: opacity var(--transition-normal) ease, transform var(--transition-normal) ease;
    
    &[data-open] {
        opacity: 1;
        transform: translateY(0);
    }
}

.workbook-card__manage-item {
    display: flex;
    padding: var(--spacing-spacing-sm-3) var(--spacing-spacing-m-2) var(--spacing-spacing-sm-3) var(--spacing-spacing-m-2);
    justify-content: flex-start;
    align-items: center;
    gap: var(--spacing-spacing-sm-2);
    align-self: stretch;
    font-family: var(--font-roboto);
    font-size: var(--font-size-sm);
    color: var(--text-text-invert);
    background: transparent;
    border: none;
    border-radius: var(--border-radius-border-radius-m);
    cursor: pointer;
    transition: transform var(--transition-normal) ease;
    
    &:hover {
        transform: translateX(var(--spacing-spacing-sm-2)) scale(1.10);
    }
    
    &:focus {
        outline: none;
        transform: translateX(var(--spacing-spacing-sm-2)) scale(1.03);
    }
    
    &:active {
        transform: translateX(var(--spacing-spacing-sm-2)) scale(1.03);
    }
}

.workbook-card__manage-icon {
    width: var(--spacing-spacing-m-1);
    height: var(--spacing-spacing-m-1);
    flex-shrink: 0;
}

.workbook-deleting {
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
    
    .workbook-deleting-spinner {
        height: 100%;
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
    }
}

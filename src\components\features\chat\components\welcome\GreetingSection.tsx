import React from 'react';
import { useThemeStyles } from '@/hooks/useThemeStyles';
import { ChatType } from '@/api/chatApi';

interface ExtendedGreetingSectionProps {
  selectedChatType?: ChatType;
}

const GreetingSection: React.FC<ExtendedGreetingSectionProps> = ({ selectedChatType = 'General' }) => {
  const { classes } = useThemeStyles();

  const getTitleByChatType = (chatType: ChatType) => {
    switch (chatType) {
      case 'Code':
        return 'Debugging is my Specialty';
      case 'Medical':
        return 'Answers are a prompt away';
      case 'Policy':
        return 'Sounds like a Policy question';
      case 'General':
      default:
        return 'How can I help today?';
    }
  };

  return (
    <div
      className={`greeting-section flex w-[800px] py-[24px] px-[32px] justify-center items-center gap-[10px] ${classes.text}`}
    >
      <h1 className={`greeting-section__title text-[40px] font-medium font-sofia-pro`}>
        {getTitleByChatType(selectedChatType)}</h1>
    </div>
  );
};

export default GreetingSection;

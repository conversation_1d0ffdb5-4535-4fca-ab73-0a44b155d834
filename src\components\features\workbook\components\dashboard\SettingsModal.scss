/* ===== MODAL LAYOUT ===== */
.settings-modal-overlay {
  position: fixed;
  inset: 0;
  z-index: var(--z-index-backdrop);
  background-color: var(--modal-backdrop-color);
  backdrop-filter: blur(var(--blur-base));
}

.settings-modal {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: var(--z-index-modal);
  width: var(--modal-width-lg);
  display: flex;
  flex-direction: column;
  background: var(--elements-surface-surface-brand);
  border-radius: var(--border-radius-border-radius-xxxl);
  box-shadow: var(--shadow-xl);
  overflow: hidden;
  outline: none;

  @media (max-width: 1024px) { 
    width: min(var(--modal-width-md), 90vw); 
  }
}

.settings-modal__close-btn {
  position: absolute;
  top: var(--spacing-spacing-m-2);
  right: var(--spacing-spacing-m-2);
  background: none;
  border: none;
  color: var(--text-text-invert);
  cursor: pointer;
  padding: var(--spacing-spacing-sm-2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all var(--transition-normal) ease;

  &:hover {
    background: var(--modal-hover-background);
    opacity: var(--opacity-80);
  }

  &:focus-visible {
    outline: var(--border-weight-border-weight-m) solid var(--text-text-invert);
    outline-offset: var(--border-weight-border-weight-m);
  }
}

.settings-modal__content {
  flex: 1;
  padding: var(--spacing-spacing-l-1) var(--spacing-spacing-l-3) var(--spacing-spacing-m-2);
  display: flex;
  flex-direction: column;
  align-items: flex-start;

  @media (max-width: 1024px) { 
    padding: var(--spacing-spacing-l-2) var(--spacing-spacing-m-3) var(--spacing-spacing-m-1); 
  }
}

.settings-modal__title {
  font-family: var(--font-sofia-pro);
  font-size: var(--font-size-6xl);
  font-weight: var(--font-weight-medium);
  line-height: 1.3;
  color: var(--text-text-invert);
  margin: 0 0 var(--spacing-spacing-l-1) 0;

  @media (max-width: 1024px) { 
    font-size: var(--font-size-4xl); 
    margin-bottom: var(--spacing-spacing-m-3); 
  }
}

/* ===== TEMPERATURE SECTION ===== */
.settings-modal__temperature-section {
  width: 100%;
}

.settings-modal__temperature-title {
  font-family: var(--font-sofia-pro);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-bold);
  color: var(--text-text-invert);
  margin: 0 0 var(--spacing-spacing-sm-3) 0;
}

.settings-modal__temperature-description {
  width: var(--component-width-xl);
  color: var(--modal-text-secondary);
  font-family: var(--font-roboto);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-normal);
  line-height: 1.2;
  margin: 0 0 var(--spacing-spacing-m-2) 0;

  @media (max-width: 1024px) { width: 100%; }
}

/* ===== RADIO COMPONENTS ===== */
.settings-modal__radio-group {
  height: var(--component-height-sm);
  padding: var(--spacing-spacing-sm-2);
  display: flex;
  align-items: center;
  gap: var(--spacing-spacing-m-3);
  margin: 0 0 var(--spacing-spacing-l-1) 0;

  @media (max-width: 1024px) {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-spacing-m-1);
  }
}

.settings-modal__radio-option {
  display: flex;
  align-items: center;
  gap: var(--spacing-spacing-sm-2);
  padding: var(--spacing-spacing-sm-2);
  color: var(--text-text-invert);
  font-family: var(--font-roboto);
  font-size: var(--font-size-sm);
  cursor: pointer;
}

.settings-modal__radio-input {
  display: flex;
  width: var(--radio-size);
  height: var(--radio-size);
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  border: var(--border-weight-border-weight-m) solid var(--text-text-invert);
  background-color: transparent;
  transition: background-color var(--transition-normal) ease;

  &:focus-visible {
    outline: var(--border-weight-border-weight-m) solid var(--button-button-primary);
    outline-offset: var(--border-weight-border-weight-m);
  }
}

.settings-modal__radio-indicator {
  display: flex;
  align-items: center;
  justify-content: center;

  &[data-unchecked] {
    display: none;
  }

  &::before {
    content: '';
    width: var(--spacing-spacing-m-1);
    height: var(--spacing-spacing-m-1);
    border-radius: 50%;
    background-color: var(--text-text-invert);
  }
}

/* ===== ACTIONS ===== */
.settings-modal__actions {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  margin-top: auto;
}

.settings-modal__button {
  display: flex;
  align-items: center;
  font-family: var(--font-roboto);
  font-size: var(--font-size-base);
  cursor: pointer;
  border: none;
  transition: all var(--transition-slow) ease;
  outline: none;

  &:focus-visible {
    outline: var(--border-weight-border-weight-m) solid var(--button-button-primary);
    outline-offset: var(--border-weight-border-weight-m);
  }

  &--cancel {
    background: transparent;
    color: var(--text-text-invert);
    padding: var(--spacing-spacing-sm-3) var(--spacing-spacing-m-2);

    &:hover {
      opacity: var(--opacity-80);
    }
  }

  &--save {
    gap: var(--spacing-spacing-sm-2);
    padding: var(--spacing-spacing-m-1) var(--spacing-spacing-m-3);
    background: var(--button-button-primary);
    border-radius: var(--border-radius-border-radius-full);
    color: var(--text-text-invert);

    &:hover {
      background-color: var(--button-hover-primary);
    }
  }
}


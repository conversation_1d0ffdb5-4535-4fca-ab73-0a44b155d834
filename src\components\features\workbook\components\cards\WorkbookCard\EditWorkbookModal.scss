.edit-workbook-modal-overlay {
  position: fixed;
  inset: 0;
  z-index: var(--z-index-backdrop);
  background-color: var(--modal-backdrop-color);
  backdrop-filter: blur(var(--blur-base));
}

.edit-workbook-modal {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: var(--z-index-modal);
  width: var(--modal-width-lg);
  display: flex;
  flex-direction: column;
  border-radius: var(--border-radius-border-radius-xxxl);
  background: var(--elements-surface-surface-brand);
  color: var(--text-text-invert);
  box-shadow: var(--shadow-xl);
  outline: none;
  max-height: 90vh;
  overflow: hidden;
}

.edit-workbook-modal__content {
  padding: var(--spacing-spacing-l-1) var(--spacing-spacing-l-3) var(--spacing-spacing-m-3) var(--spacing-spacing-l-3);
  display: flex;
  flex-direction: column;
  overflow-y: auto;
}

.edit-workbook-modal__title {
  font-family: var(--font-sofia-pro);
  font-size: var(--font-size-4xl);
  font-weight: var(--font-weight-semibold);
  color: var(--text-text-invert);
  margin-bottom: var(--spacing-spacing-l-1);
}

.edit-workbook-modal__info-message {
  display: flex;
  align-items: center;
  align-self: stretch;
  gap: var(--spacing-spacing-sm-3);
  margin-bottom: var(--spacing-spacing-m-2);

  span {
    text-align: center;
    color: var(--brand-hmk-primary-200);
    font-family: var(--font-roboto);
    font-size: var(--font-size-base);
    font-style: normal;
    font-weight: var(--font-weight-normal);
    line-height: 130%;
  }
}

.edit-workbook-modal__input-container {
  position: relative;
  width: 100%;
  margin-bottom: var(--spacing-spacing-m-3);
}

.edit-workbook-modal__input {
  width: 100%;
  padding: var(--spacing-spacing-m-1) var(--spacing-spacing-l-3) var(--spacing-spacing-m-1) var(--spacing-spacing-m-2);
  border: var(--border-weight-border-weight-s) solid var(--brand-hmk-shades-400);
  border-radius: var(--border-radius-border-radius-m);
  background: var(--modal-input-background);
  color: var(--text-text-invert);
  font-family: var(--font-roboto);
  font-size: var(--font-size-base);
  outline: none;
  transition: border-color var(--transition-normal) ease;

  &:focus {
    border-color: var(--border-color-border-highlight);
  }

  &::placeholder {
    color: var(--modal-placeholder-color);
  }
}

.edit-workbook-modal__input-icon {
  position: absolute;
  right: var(--spacing-spacing-m-1);
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-text-invert);
  width: var(--spacing-spacing-m-3);
  height: var(--spacing-spacing-m-3);
}

.edit-workbook-modal__system-instructions {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.edit-workbook-modal__section-title {
  height: var(--section-title-height);
  width: var(--component-width-xl);
  color: var(--neutral-white);
  font-family: var(--font-sofia-pro);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-bold);
  font-style: normal;
  line-height: 1.3;
  margin-bottom: var(--spacing-spacing-sm-3);
}

.edit-workbook-modal__section-description {
  width: var(--component-width-xl);
  color: var(--brand-hmk-primary-200);
  font-family: var(--font-roboto);
  font-size: var(--font-size-sm);
  font-style: normal;
  font-weight: var(--font-weight-normal);
  line-height: 1.2;
  margin: 0 0 var(--spacing-spacing-sm-3) 0;
}

.edit-workbook-modal__textarea-container {
  position: relative;
  width: 100%;
  margin-bottom: var(--spacing-spacing-m-3);
}

.edit-workbook-modal__textarea {
  width: 100%;
  height: var(--component-height-xl);
  padding: var(--spacing-spacing-sm-3) var(--spacing-spacing-m-3) var(--spacing-spacing-m-3) var(--spacing-spacing-m-3);
  border: var(--border-weight-border-weight-s) solid var(--brand-hmk-shades-400);
  border-radius: var(--border-radius-border-radius-m);
  background: var(--modal-input-background);
  color: var(--text-text-invert);
  font-family: var(--font-roboto);
  font-size: var(--font-size-base);
  outline: none;
  transition: border-color var(--transition-normal) ease;
  resize: none;
  display: flex;
  flex-direction: column;
  align-items: center;

  &:focus {
    border-color: var(--border-color-border-highlight);
  }

  &::placeholder {
    color: var(--modal-placeholder-color);
  }
}

.edit-workbook-modal__textarea-icon {
  position: absolute;
  right: var(--spacing-spacing-m-1);
  top: var(--spacing-spacing-m-1);
  color: var(--text-text-invert);
  width: var(--spacing-spacing-m-3);
  height: var(--spacing-spacing-m-3);
}

.edit-workbook-modal__footer {
  border-top: var(--border-weight-border-weight-s) solid var(--brand-hmk-primary-400);
  padding: var(--spacing-spacing-m-3) var(--spacing-spacing-l-3);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.edit-workbook-modal__cancel-button {
  padding: var(--spacing-spacing-sm-3) var(--spacing-spacing-m-2);
  display: flex;
  align-items: center;
  gap: var(--spacing-spacing-sm-3);
  border-radius: var(--border-radius-border-radius-full);
  border: 1.5px solid var(--brand-hmk-primary-400);
  background: transparent;
  color: var(--text-text-invert);
  font-family: var(--font-roboto);
  font-size: var(--font-size-base);
  font-style: normal;
  font-weight: var(--font-weight-normal);
  text-align: center;
  cursor: pointer;
  transition: all var(--transition-normal) ease;
  
  &:hover:not(:disabled) {
    background: var(--modal-hover-background);
  }
  
  &:disabled {
    opacity: var(--opacity-60);
    cursor: not-allowed;
  }
}

.edit-workbook-modal__save-button {
  padding: var(--spacing-spacing-sm-3) var(--spacing-spacing-m-2);
  display: flex;
  align-items: center;
  gap: var(--spacing-spacing-sm-3);
  border-radius: var(--border-radius-border-radius-full);
  border: var(--border-weight-border-weight-s) solid var(--brand-hmk-primary-400);
  background: var(--brand-hmk-primary-400);
  color: var(--text-text-invert);
  font-family: var(--font-roboto);
  font-size: var(--font-size-base);
  font-style: normal;
  font-weight: var(--font-weight-normal);
  cursor: pointer;
  transition: all var(--transition-normal) ease;
  
  &:hover:not(:disabled) {
    background: var(--button-hover-primary);
  }
  
  &:disabled {
    opacity: var(--opacity-60);
    cursor: not-allowed;
  }
}
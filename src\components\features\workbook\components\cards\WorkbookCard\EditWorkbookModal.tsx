import React, { useState, useEffect } from 'react';
import { MdOutlineModeEdit } from 'react-icons/md';
import { IoMdInformationCircleOutline } from 'react-icons/io';
import { IoClose } from 'react-icons/io5';
import { GiCancel } from 'react-icons/gi';
import { Dialog } from '@base-ui-components/react/dialog';
import SaveButton from '@/components/common/icons/SaveButton';
import './EditWorkbookModal.scss';

interface EditWorkbookModalProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  onSaveWorkbook: (newName: string, systemInstructions?: string) => Promise<void>;
  currentName: string;
  currentSystemInstructions?: string;
  isUpdating?: boolean;
}

const EditWorkbookModal: React.FC<EditWorkbookModalProps> = ({
  isOpen,
  onOpenChange,
  onSaveWorkbook,
  currentName,
  currentSystemInstructions = '',
  isUpdating = false,
}) => {
  const [workbookName, setWorkbookName] = useState(currentName);
  const [systemInstructions, setSystemInstructions] = useState(currentSystemInstructions);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Reset the input when modal opens
  useEffect(() => {
    if (isOpen) {
      setWorkbookName(currentName);
      setSystemInstructions(currentSystemInstructions);
    }
  }, [isOpen, currentName, currentSystemInstructions]);

  const handleCancel = () => {
    setWorkbookName(currentName);
    setSystemInstructions(currentSystemInstructions);
    onOpenChange(false);
  };

  const handleSave = async () => {
    if (!workbookName.trim() || (workbookName.trim() === currentName && systemInstructions.trim() === currentSystemInstructions)) {
      return;
    }

    setIsSubmitting(true);
    try {
      await onSaveWorkbook(workbookName.trim(), systemInstructions.trim());
      onOpenChange(false);
    } catch (error) {
      console.error('Error saving workbook:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter' && !isSubmitting && workbookName.trim() && 
        (workbookName.trim() !== currentName || systemInstructions.trim() !== currentSystemInstructions)) {
      handleSave();
    } else if (e.key === 'Escape') {
      handleCancel();
    }
  };

  const isButtonDisabled = !workbookName.trim() || 
    (workbookName.trim() === currentName && systemInstructions.trim() === currentSystemInstructions) || 
    isSubmitting || isUpdating;

  return (
    <Dialog.Root open={isOpen} onOpenChange={onOpenChange}>
      <Dialog.Portal>
        <Dialog.Backdrop className="edit-workbook-modal-overlay" />
        <Dialog.Popup className="edit-workbook-modal">
          {/* Close Button */}
          <Dialog.Close className="edit-workbook-modal__close-btn" aria-label="Close modal">
            <IoClose size={24} />
          </Dialog.Close>

          <div className="edit-workbook-modal__content">
            {/* Header */}
            <Dialog.Title className="edit-workbook-modal__title">Edit Workbook</Dialog.Title>

            {/* Info Message */}
            <div className="edit-workbook-modal__info-message">
              <IoMdInformationCircleOutline size={24} />
              <span>All files required unless marked optional.</span>
            </div>

            {/* Name Input */}
            <div className="edit-workbook-modal__input-container">
              <input
                type="text"
                value={workbookName}
                onChange={e => setWorkbookName(e.target.value)}
                onKeyDown={handleKeyPress}
                className="edit-workbook-modal__input"
                placeholder="Enter workbook name"
                autoFocus
                disabled={isSubmitting || isUpdating}
              />
              <MdOutlineModeEdit className="edit-workbook-modal__input-icon" />
            </div>

            {/* System Instructions Section */}
            <section className="edit-workbook-modal__system-instructions">
              <h3 className="edit-workbook-modal__section-title">System Instructions (optional)</h3>
              <p className="edit-workbook-modal__section-description">
                System instructions in Gemini models guide the model's behavior by providing additional context and
                constraints beyond the user's prompt. They can be used to define a persona, output format, style, tone,
                and goals, influencing the model's responses.
              </p>
              <div className="edit-workbook-modal__textarea-container">
                <textarea
                  className="edit-workbook-modal__textarea"
                  value={systemInstructions}
                  onChange={e => setSystemInstructions(e.target.value)}
                  placeholder="Enter your system instructions here..."
                  disabled={isSubmitting || isUpdating}
                />
                <MdOutlineModeEdit className="edit-workbook-modal__textarea-icon" />
              </div>
            </section>
          </div>

          {/* Footer */}
          <div className="edit-workbook-modal__footer">
            <button
              type="button"
              onClick={handleCancel}
              className="edit-workbook-modal__cancel-button"
              disabled={isSubmitting || isUpdating}
            >
              <GiCancel size={24} />
              <span>Cancel</span>
            </button>
            <button
              type="button"
              onClick={handleSave}
              className="edit-workbook-modal__save-button"
              disabled={isButtonDisabled}
            >
              <SaveButton size={24} />
              <span>{isSubmitting || isUpdating ? 'Saving...' : 'Save Workbook'}</span>
            </button>
          </div>
        </Dialog.Popup>
      </Dialog.Portal>
    </Dialog.Root>
  );
};

export default EditWorkbookModal;

.input-lower-tray {
  display: flex;
  width: 100%;
  align-items: center;
  justify-content: flex-end;
  position: absolute;
  bottom: var(--spacing-spacing-m-1);
  right: var(--spacing-spacing-m-3);

  &__button-group {
    display: flex;
    align-items: center;
    gap: var(--spacing-spacing-m-1);
  }

  &__temperature-badge {
    display: flex;
    align-items: center;
    padding: var(--spacing-spacing-sm-3) var(--spacing-spacing-m-1);
    border-radius: var(--border-radius-border-radius-l);
    backdrop-filter: blur(var(--blur-md));
    white-space: nowrap;
    flex-shrink: 0;
    // Remove min-width to allow natural sizing
    width: fit-content;
  }

  &__temperature-icon {
    flex-shrink: 0;
    width: var(--spacing-spacing-m-3);
    height: var(--spacing-spacing-m-3);
  }

  &__temperature-text {
    font-family: var(--font-roboto);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    color: var(--text-text-invert);
    line-height: 1;
    // Remove flex: 1 and text-align: center to prevent stretching
    overflow: visible;
    text-overflow: clip;
  }

  &__settings-button,
  &__send-button {
    // These will use the circleIconButton class from useThemeStyles
    font-family: var(--font-roboto);
  }

  &__settings-icon,
  &__send-icon {
    width: var(--spacing-spacing-l-1);
    height: var(--spacing-spacing-l-1);
  }

  // Responsive adjustments for smaller screens
  @media (max-width: 768px) {
    &__temperature-badge {
      padding: var(--spacing-spacing-sm-1) var(--spacing-spacing-sm-2);
    }

    &__temperature-text {
      font-size: var(--font-size-xs);
    }
  }
}

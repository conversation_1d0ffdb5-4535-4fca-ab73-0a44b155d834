import React, { useState } from 'react';
import { ChatMessageProps } from '@/components/features/chat/chatTypes';
import { useThemeStyles } from '@/hooks/useThemeStyles';
import MarkdownRenderer from '../../../common/markdown/MarkdownRenderer';
import MessageActions from './messageAction/MessageActions';
import PolicyWebMessageActions from '../../policy_web/components/PolicyWebMessageActions';
import { useCopyAction } from './messageAction/hooks/useMessageActions';
import clsx from 'clsx';
import {
  getChatMessageFileIcon,
  displaySize,
} from '@/components/features/chat/components/fileUpload/utils/fileDisplayUtils';
import { useAppSelector } from '@/store/hooks';
import { selectSelectedChatType } from '@/store/slices/chatSlice';

const ChatMessage: React.FC<ChatMessageProps> = ({
  message,
  handleFeedbackUp,
  handleFeedbackDown,
}) => {
  const { classes } = useThemeStyles();
  const [isHovered, setIsHovered] = useState(false);
  const selectedChatType = useAppSelector(selectSelectedChatType);

  const block = 'chat-message';

  const isUser = message.role === 'user';
  const isModel = message.role === 'model';
  const isSystem = message.role === 'system';

  const { state: actionState, handleCopy } = useCopyAction(isModel ? message.text : '');

  const containerBaseClasses = 'flex mb-2 w-full overflow-hidden';
  const containerAlignment = isUser ? 'justify-end' : 'justify-start';

  const hasFiles = message.fileInfo && message.fileInfo.length > 0;

  // if (isUser && hasFiles) {
  //   console.log('[ChatMessage] User message with files:', JSON.stringify(message, null, 2));
  // }

  return (
    <div
      className={clsx(block, containerBaseClasses, containerAlignment, classes.text, {
        [`${block}--user`]: isUser,
        [`${block}--model`]: isModel,
        [`${block}--system`]: isSystem,
      })}
      onMouseEnter={() => isModel && setIsHovered(true)}
      onMouseLeave={() => isModel && setIsHovered(false)}
    >
      <div
        className={clsx(
          `${block}__bubble`,
          'inline-block max-w-[90%] py-3 px-4 shadow-md transition-all duration-200 ease-in-out overflow-hidden',
          {
            [`${block}__bubble--user rounded-xl bg-[#002D4F] text-white`]: isUser,
            [`${block}__bubble--model w-full max-w-full relative pb-10`]: isModel,
            [`${block}__bubble--system rounded-xl border border-red-300 dark:border-red-700 bg-red-50 dark:bg-red-900/20 text-red-600 dark:text-red-400 italic`]:
              isSystem,
          }
        )}
      >
        {hasFiles && (
          <div
            className={clsx(
              `${block}__file-attachments`,
              'mb-2 p-2 rounded-xl border shadow-lg shadow-blue-900/20',
              isUser
                ? 'bg-[#123A5D] border-[#2365A8]'
                : 'bg-gray-100 dark:bg-gray-700 border-gray-300 dark:border-gray-600'
            )}
          >
            <h4
              className={clsx(
                `${block}__file-attachments-header`,
                'flex items-center text-xs font-semibold mb-1.5 pb-1 border-b text-blue-200 border-[#2365A8]'
              )}
            >
              Attached Files
              <span
                className={clsx(
                  `${block}__file-count-badge`,
                  'ml-2 bg-blue-500/20 text-blue-200 px-2 py-0.5 rounded-full text-xs'
                )}
              >
                {message.fileInfo!.length}
              </span>
            </h4>
            {message.fileInfo?.map((file, index) => {
              const isExpired = file.isExpired === true;
              return (
                <div
                  key={`${file.gcs_uri}-${index}`}
                  className={clsx(
                    `${block}__file-item`,
                    'flex items-center p-2 my-1 rounded-md group cursor-pointer',
                    isUser
                      ? 'bg-[#175084] hover:bg-[#2365A8]'
                      : 'bg-gray-200 dark:bg-gray-600 hover:bg-gray-300 dark:hover:bg-gray-700',
                    {
                      'expired-gradient opacity-90': isExpired,
                    }
                  )}
                  title={
                    isExpired ? 'This file has expired and may need to be re-uploaded.' : file.name
                  }
                >
                  <span
                    className={clsx(
                      `${block}__file-icon-wrapper`,
                      'mr-3 flex-shrink-0 transition-transform duration-150 group-hover:scale-110'
                    )}
                  >
                    {getChatMessageFileIcon(file.type, isExpired)}
                  </span>
                  <div className={clsx(`${block}__file-details`, 'flex-grow min-w-0')}>
                    <p
                      className={clsx(
                        `${block}__file-name`,
                        'truncate font-semibold',
                        isExpired
                          ? [`${block}__file-name--expired`, 'text-red-600 dark:text-red-400']
                          : 'text-blue-50 group-hover:text-white'
                      )}
                      title={file.name}
                    >
                      {file.name}
                      {isExpired && (
                        <span
                          className={clsx(
                            `${block}__expired-badge`,
                            'ml-2 inline-block px-2 py-0.5 bg-red-500 text-white uppercase text-xs font-medium rounded'
                          )}
                        >
                          Expired
                        </span>
                      )}
                    </p>
                    <p
                      className={clsx(
                        `${block}__file-meta`,
                        'text-xs text-gray-500 dark:text-gray-400'
                      )}
                    >
                      {displaySize(file.size)}
                    </p>
                  </div>
                </div>
              );
            })}
          </div>
        )}

        {message.text &&
          (isModel ? (
            <MarkdownRenderer
              content={message.text}
              className={clsx(
                `${block}__markdown-content`,
                'break-words w-full overflow-x-hidden max-w-full'
              )}
              isUser={false}
              isPolicyContext={message.isPolicyContext ?? false}
            />
          ) : (
            <p
              className={clsx(
                `${block}__text-content`,
                'break-words whitespace-pre-wrap w-full overflow-x-hidden max-w-full'
              )}
            >
              {message.text}
            </p>
          ))}

        {!message.text && hasFiles && isUser && (
          <div
            className={clsx(
              `${block}__text-content`,
              `${block}__text-content--file-placeholder`,
              'italic text-gray-300 dark:text-gray-400 text-xs mt-1 break-words w-full overflow-x-hidden max-w-full'
            )}
          >
            (File(s) attached with this message)
          </div>
        )}

        {isModel && (
          <div className={clsx(`${block}__actions-wrapper`, 'mt-2')}>
            {message.isPolicyContext && message.policyWebData ? (
              <PolicyWebMessageActions
                isHovered={isHovered}
                messageText={message.text}
                sessionId={message.policyWebData.sessionId || ''}
                responseId={message.policyWebData.responseId || message.id}
                responseClass={message.policyWebData.responseClass}
                feedback={message.feedback}
                messageId={message.id}
              />
            ) : (
              <MessageActions
                isHovered={isHovered}
                feedback={message.feedback}
                messageText={message.text}
                selectedChatType={selectedChatType}
                state={actionState}
                handlers={{
                  handleCopy,
                  handleThumbsUp: handleFeedbackUp,
                  handleThumbsDown: handleFeedbackDown,
                }}
              />
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default React.memo(ChatMessage);

import json
import sys
import re
from datetime import datetime, timezone, timedelta
from typing import List, Optional, overload

from firebase_admin.firestore import firestore
from google.protobuf.timestamp_pb2 import Timestamp
import google.cloud.exceptions as GCPExceptions

from models.shared.enums import ModelTemperatures
from models.retrieval_workbook.RetrievalEnums import RetrievalWorkbookChunkSize
from models.retrieval_workbook.RetrievalSession import RetrievalSession
from models.retrieval_workbook.RetrievalWorkbook import RetrievalWorkbook, CreateRetrievalWorkbook, RetrievalWorkbookSummary, UpdateRetrievalWorkbook
from models.retrieval_workbook.RetrievalFile import CreateRetrievalFile, RetrievalFile

from services.RetrievalSessionService import RetrievalSessionService
from services.RetrievalFileService import RetrievalFileService
from services.VertexAISearchService import VertexAISearchService

from website.config import get_config_val
from website.google_cloud_init import fetch_ENV_by_project_id
from website.extensions import oidc_variables
from lib.firestore_client import FirestoreLimits, FirestoreLimitException, FirestoreClient


class RetrievalWorkbookService:

    def __init__(self, persistence_client: firestore.Client, global_persistence_client: firestore.Client = None):
        self._persistence_client: firestore.Client = persistence_client
        self._global_persistence_client: firestore.Client = global_persistence_client if global_persistence_client else FirestoreClient.GlobalInstanceClient()
        self._retrieval_file_service: RetrievalFileService = RetrievalFileService(
            persistence_client=self._persistence_client,
            global_persistence_client=self._global_persistence_client
        )

    @staticmethod
    def get_domain_qualified_workbook_id(workbook: RetrievalWorkbook, is_global: bool = False) -> str:
        domain_basename = ""
        if is_global:
            domain_basename = "global"
        else:
            domain = workbook.author.strip().lower().split("@")[1]
            domain_basename = domain.split(".")[0]

        domain_qualified_workbook_id = f"{domain_basename}_{workbook.id}"
        return domain_qualified_workbook_id

    @staticmethod
    def get_vais_workbook_document_id(workbook: RetrievalWorkbook | str, file: RetrievalFile | str, is_global: bool = False) -> str:
        file_id = ""
        if isinstance(file, RetrievalFile):
            file_id = file.id
        elif isinstance(file, str):
            file_id = file
        return f"{RetrievalWorkbookService.get_domain_qualified_workbook_id(workbook, is_global)}_{file_id}"

    @staticmethod
    def _generate_vais_document_for_workbook(workbook: RetrievalWorkbook, files: List[RetrievalFile], is_global: bool = False):
        workbook_domain = "global" if is_global else workbook.author.strip(
        ).lower().split("@")[1]
        vais_documents = [
            VertexAISearchService.generate_document(
                document_id=RetrievalWorkbookService.get_vais_workbook_document_id(
                    workbook, file, is_global),
                metadata={"title": file.name, "author": workbook.author,
                          "workbook_id": workbook.id, "domain": workbook_domain},
                content={"mime_type": file.mime_type, "uri": file.gcs_path}
            ) for file in files
        ]

        return vais_documents

    def create_workbook(
        self,
        workbook_name: str,
        author_email: str,
        description: Optional[str] = None,
        document_id: Optional[str] = None,
        system_instructions: Optional[str] = None,
        temperature: float = ModelTemperatures.MIN_TEMPERATURE,
        chunk_size: RetrievalWorkbookChunkSize = RetrievalWorkbookChunkSize.large,
        tags: List[str] = [],
        authorized_readers: List[str] = [],
        authorized_writers: List[str] = [],
    ) -> RetrievalWorkbook:
        """
        `create_workbook` creates a `RetrievalWorkbook` and attempts to persist it

        Parameters
        ----------
        `authorized_entities` : List[str]
            array of Active Directory security principles that have read access to this workbook
        `document_id` : Optional[str]
            used to override any auto-generated IDs

        Returns
        -------
        `RetrievalWorkbook`

        Raises
        ------
        `FirestoreLimitException`
            Local exception class that reports the parameter that was exceeded and its value
        `google.cloud.exceptions.Conflict`
            If a `RetrievalWorkbook` with the same `document_id` already exists
        `google.api_core.exceptions.InvalidArgument`
            Catch-all error to expect for unhandled/irrecoverable Firestore issues
        `pydantic_core.ValidationError`
        """
        workbook = CreateRetrievalWorkbook(
            name=workbook_name,
            author=author_email,
            description=description,
            system_instructions=system_instructions,
            chunk_size=chunk_size,
            temperature=temperature,
            tags=tags,
            authorized_entities=[author_email, *
                                 authorized_readers, *authorized_writers],
            authorizedReaders=[author_email, *authorized_readers],
            authorizedWriters=[author_email, *authorized_writers]
        )

        # TODO: setup directory structure: gcs://workbooks/{workbook_id} as a `ManagedFolder`
        author_domain = author_email.split('@')[-1].lower()
        # TODO @preston to refactor get_search_config
        VAIS = VertexAISearchService(author_domain, chunk_size=chunk_size)
        session_service = RetrievalSessionService(
            persistence_client=self._persistence_client, global_persistence_client=self._global_persistence_client)
        workbook.data_store_id = VAIS.data_store_id
        workbook.search_app_id = VAIS.search_app_id

        document_string_size_bytes = sys.getsizeof(
            workbook.to_dict(date_format_iso=True))
        if document_string_size_bytes >= FirestoreLimits.DOCUMENT_SIZE_BYTES:
            raise FirestoreLimitException.create_document_size_limit_exception(
                document_string_size_bytes)

        collection_ref = self._persistence_client\
            .collection(RetrievalWorkbook.COLLECTION_NAME)
        as_dict = workbook.to_dict(
            to_exclude={'files': True, 'sessions': True})

        created_at, workbook_ref = collection_ref.add(
            document_data=as_dict,
            document_id=document_id
        )
        new_workbook = RetrievalWorkbook(
            id=workbook_ref.id, **workbook.to_dict(date_format_iso=False))
        workbook_user_session = session_service.create_workbook_session(
            new_workbook.id, author_email, [])
        new_workbook.sessions = [workbook_user_session]
        return new_workbook

    def _get_workbook_by_id(
        self,
        id: str,
        user_email: str | None = None,
        with_files: bool = False,
        with_sessions: bool = False,
        is_global: bool = False,
        author: bool = False,
        authorize_user: bool = False,
    ) -> RetrievalWorkbook | None:
        """
        Base method to fetch a `RetrievalWorkbook`.

        Parameters
        ----------
        `id` : str
            The `id` upon which a `RetrievalWorkbook` will be matched on.
        `user_email` : str | None
            User email for authorization (if `authorize_user` is True) and session filtering.
        `with_files` : bool
            Whether or not to retrieve uploaded files associated with the workbook.
        `with_sessions` : bool
            Whether or not to retrieve sessions associated with the workbook and user.
        `is_global` : bool
            Whether to use the global persistence client.
        `author` : bool
            Whether to check if the user is the author of the workbook.
        `authorize_user` : bool
            Whether to check if the user is authorized to access the workbook.

        Returns
        -------
        `RetrievalWorkbook | None`
            A single `RetrievalWorkbook` whose id matches `id` or `None` if it does not exist or the user is not authorized or the author.

        Raises
        ------
        `google.api_core.exceptions.InvalidArgument`
            Catch-all error to expect for unhandled/irrecoverable Firestore issues.
        `pydantic_core.ValidationError`
            This may be raised if the `RetrievalWorkbook` with id `id` has an invalid format.
        """
        client = (
            self._global_persistence_client if is_global else self._persistence_client
        )
        workbook_ref = client.collection(
            RetrievalWorkbook.COLLECTION_NAME).document(id)

        workbook_doc = workbook_ref.get()
        if workbook_doc.exists:
            as_dict = workbook_doc.to_dict()

            if author and (user_email is None or user_email != as_dict["author"]):
                return None

            if not is_global:
                if authorize_user and (
                    user_email is None
                    or user_email not in as_dict.get("authorized_entities", [])
                ):
                    return None

            workbook = RetrievalWorkbook(id=workbook_doc.id, **as_dict)

            if with_files:
                file_service = RetrievalFileService(
                    persistence_client=self._global_persistence_client if is_global else self._persistence_client,
                    global_persistence_client=self._global_persistence_client,
                )
                files = file_service.get_files_by_workbook_id(
                    workbook_id=workbook.id)
                workbook.files = files

            if with_sessions and user_email is not None:
                session_service = RetrievalSessionService(
                    persistence_client=self._persistence_client,
                    global_persistence_client=self._global_persistence_client,
                )
                sessions = session_service.get_user_sessions_by_workbook_id(
                    user_email=user_email, workbook_id=workbook.id
                )
                workbook.sessions = sessions

            return workbook
        return None

    def get_workbook_by_id(
        self,
        id: str,
        with_files: bool = False,
        with_sessions: bool = False,
        user_email: str | None = None,
        is_global: bool = False,
    ) -> RetrievalWorkbook | None:
        """
        `get_workbook_by_id` fetches and returns a shallow `RetrievalWorkbook` matching `id`

        Parameters
        ----------
        `id` : str
            The `id` upon which a `RetrievalWorkbook` will be matched on
        `with_files` : bool
            Whether or not to retrieve uploaded files associated with the workbook
        `with_sessions` : bool
            Whether or not to retrieve sessions associated with the workbook and user
        `user_email` : str | None
            Used when `with_sessions` is True to filter workbook sessions by user_email

        Returns
        -------
        `RetrievalWorkbook | None`
            A single `RetrievalWorkbook` whose id matches `id` or `None` if it does not exist

        Raises
        ------
        `google.api_core.exceptions.InvalidArgument`
            Catch-all error to expect for unhandled/irrecoverable Firestore issues
        `pydantic_core.ValidationError`
            This may be raised if the `RetrievalWorkbook` with id `id` has an invalid format
        """
        return self._get_workbook_by_id(
            id=id,
            user_email=user_email,
            with_files=with_files,
            with_sessions=with_sessions,
            is_global=is_global,
        )

    def get_authorized_workbook_by_id(
        self,
        id: str,
        user_email: str,
        with_files: bool = False,
        with_sessions: bool = False,
        is_global: bool = False,
    ) -> RetrievalWorkbook | None:
        """
        `get_authorized_workbook_by_id` fetches and returns a shallow `RetrievalWorkbook` matching `id`

        Parameters
        ----------
        `id` : str
            The `id` upon which a `RetrievalWorkbook` will be matched on
        `with_files` : bool
            Whether or not to retrieve uploaded files associated with the workbook
        `with_sessions` : bool
            Whether or not to retrieve sessions associated with the workbook and user
        `user_email` : str | None
            Used to get only the users workbook & when `with_sessions` is True to filter workbook sessions by user_email

        Returns
        -------
        `RetrievalWorkbook | None`
            A single `RetrievalWorkbook` whose id matches `id` or `None` if it does not exist

        Raises
        ------
        `google.api_core.exceptions.InvalidArgument`
            Catch-all error to expect for unhandled/irrecoverable Firestore issues
        `pydantic_core.ValidationError`
            This may be raised if the `RetrievalWorkbook` with id `id` has an invalid format
        """

        return self._get_workbook_by_id(
            id=id,
            user_email=user_email,
            with_files=with_files,
            with_sessions=with_sessions,
            is_global=is_global,
            authorize_user=True,
        )

    def get_author_workbook_by_id(
        self,
        id: str,
        user_email: str,
        with_files: bool = False,
        with_sessions: bool = False,
        is_global: bool = False,
    ) -> RetrievalWorkbook | None:
        """
        `get_author_workbook_by_id` fetches and returns a shallow `RetrievalWorkbook` matching `id`

        Parameters
        ----------
        `id` : str
            The `id` upon which a `RetrievalWorkbook` will be matched on
        `with_files` : bool
            Whether or not to retrieve uploaded files associated with the workbook
        `with_sessions` : bool
            Whether or not to retrieve sessions associated with the workbook and user
        `user_email` : str | None
            Used to get only the users workbook & when `with_sessions` is True to filter workbook sessions by user_email

        Returns
        -------
        `RetrievalWorkbook | None`
            A single `RetrievalWorkbook` whose id matches `id` or `None` if it does not exist

        Raises
        ------
        `google.api_core.exceptions.InvalidArgument`
            Catch-all error to expect for unhandled/irrecoverable Firestore issues
        `pydantic_core.ValidationError`
            This may be raised if the `RetrievalWorkbook` with id `id` has an invalid format
        """

        return self._get_workbook_by_id(
            id=id,
            user_email=user_email,
            with_files=with_files,
            with_sessions=with_sessions,
            is_global=is_global,
            author=True,
            authorize_user=True,
        )

    def get_workbooks_by_author(
        self,
        user_id: str
    ) -> List[RetrievalWorkbookSummary]:
        """
        `get_workbooks_by_author` fetches all shallow `RetrievalWorkbooks`
        for which `user_id` is an author

        Parameters
        ----------
        `user_id` : str

        Returns
        -------
        `List[RetrievalWorkbook]`

        Raises
        ------
        `google.api_core.exceptions.InvalidArgument`
            Catch-all error to expect for unhandled/irrecoverable Firestore issues
        `pydantic_core.ValidationError`
            This may be raised if a `RetrievalWorkbook` has an invalid format
        """
        query = self._persistence_client\
            .collection(RetrievalWorkbook.COLLECTION_NAME)\
            .where("author", "==", user_id.lower())

        workbook_docs = query.stream()
        workbooks = [
            RetrievalWorkbookSummary(id=doc.id, **(doc.to_dict()))
            for doc in workbook_docs
            if doc.exists
        ]
        for workbook in workbooks:
            workbook.num_files = self._retrieval_file_service.get_file_count_by_workbook_id(
                workbook.id, is_global=False)
        return workbooks

    def get_workbooks_by_name(self, workbook_name: str, workbook_author: str | None = None) -> List[RetrievalWorkbook]:
        """
        `get_workbook_by_name` fetches all shallow `RetrievalWorkbooks` matching `workbook_name`
        and optionally scopes it to `workbook_author`, if it is provided

        Raises
        ------
        `google.api_core.exceptions.InvalidArgument`
            Catch-all error to expect for unhandled/irrecoverable Firestore issues
        `pydantic_core.ValidationError`
            This may be raised if a `RetrievalWorkbook` has an invalid format
        """
        query = self._persistence_client\
            .collection(RetrievalWorkbook.COLLECTION_NAME)
        if workbook_author:
            query = query.where(
                "author", "==", workbook_author.strip().lower())
        query = query.where("name", "==", workbook_name.strip())

        workbook_docs = query.stream()
        workbooks = [
            RetrievalWorkbook(id=doc.id, **(doc.to_dict()))
            for doc in workbook_docs
            if doc.exists
        ]

        return workbooks

    def get_global_workbooks(self) -> List[RetrievalWorkbookSummary]:
        global_client = self._global_persistence_client
        query = global_client\
            .collection(RetrievalWorkbook.COLLECTION_NAME)

        workbook_docs = query.stream()
        workbooks = [
            RetrievalWorkbookSummary(id=doc.id, **(doc.to_dict()))
            for doc in workbook_docs
            if doc.exists
        ]
        for workbook in workbooks:
            workbook.num_files = self._retrieval_file_service.get_file_count_by_workbook_id(
                workbook.id, is_global=True)
        return workbooks

    def update_workbook(
        self,
        workbook_id: str,
        workbook_updates: UpdateRetrievalWorkbook,
    ) -> Timestamp | None:
        """
        `update_workbook` updates the public fields of `RetrievalWorkbook` matching `workbook_id`
        according to the `Optional` fields specified by the `UpdateRetrievalWorkbook` data model

        Parameters
        ----------
        `workbook_id` : str
            The `id` upon which a `RetrievalWorkbook` will be matched on
        `workbook_updates` : UpdateRetrievalWorkbook
            The available public fields of a `RetrievalWorkbook` that can be updated.
            `clear_description` is exposed in order to specify whether or not `description`
            is `None` intentionally or because it was simply not updated.

        Returns
        -------
        `datetime`
            The utc datetime at which the document update was successfully performed

        Raises
        ------
        `google.api_core.exceptions.InvalidArgument`
            Catch-all error to expect for unhandled/irrecoverable Firestore issues
        `google.cloud.exceptions.NotFound`
            If there does not exist a `RetrievalWorkbook` with id `workbook_id`
        """
        field_updates = {
            k: v for (k, v) in workbook_updates.model_dump().items() if k != 'clear_description' and v is not None
        }
        if workbook_updates.clear_description:
            field_updates['description'] = None
        field_updates['updated_utc'] = datetime.now(timezone.utc)

        workbook_ref: firestore.DocumentReference = self._persistence_client\
            .collection(RetrievalWorkbook.COLLECTION_NAME)\
            .document(workbook_id)

        update_result: firestore.types.WriteResult = workbook_ref.update(
            field_updates)
        if update_result:
            return update_result.update_time

    def delete_workbook(
        self,
        user_email: str,
        workbook_id: str,
        is_global: bool = False
    ) -> None:
        """
        `delete_workbook` deletes the workbook with id `workbook_id` and initiates any
        clean-up of resources associated with the workbook

        Parameters
        ----------
        `workbook_id` : str
            The `id` upon which a `RetrievalWorkbook` will be matched on

        Raises
        ------
        `google.api_core.exceptions.InvalidArgument`
            Catch-all error to expect for unhandled/irrecoverable Firestore issues
        """
        workbook = self.get_author_workbook_by_id(
            id=workbook_id, user_email=user_email)
        if workbook is None:
            raise Exception("Workbook is None or User doesn't have access")
        if "@" in user_email and workbook is not None:
            domain = "default" if is_global else user_email.strip(
            ).lower().split("@")[1]
            retrieval_file_service = RetrievalFileService(
                persistence_client=self._persistence_client, global_persistence_client=self._global_persistence_client)
            retrieval_session_service = RetrievalSessionService(
                persistence_client=self._persistence_client, global_persistence_client=self._global_persistence_client)
            vais_service = VertexAISearchService(
                domain=domain,
                chunk_size=workbook.chunk_size
            )

            workbook_files: List[RetrievalFile] = retrieval_file_service.get_files_by_workbook_id(
                workbook_id)
            workbook_sessions: List[RetrievalSession] = retrieval_session_service.get_sessions_by_workbook_id(
                workbook_id, with_messages=False)
            for file in workbook_files:
                retrieval_file_service.delete_workbook_file_and_artifacts(
                    user_email, workbook_id, file, vais_service, is_global=is_global)
            for session in workbook_sessions:
                retrieval_session_service.delete_session_by_id(
                    workbook_id, session.id)
            workbook_ref: firestore.DocumentReference = self._persistence_client\
                .collection(RetrievalWorkbook.COLLECTION_NAME)\
                .document(workbook_id)
            workbook_ref.delete()

    def get_workbook_file(
        self,
        workbook_id: str,
        file_id
    ) -> RetrievalFile:
        file = self._retrieval_file_service.get_file_by_id(
            workbook_id, file_id)
        return file

    def callback_upload_file(
        self,
        user_email: str,
        workbook_id: str,
        uploaded_file: CreateRetrievalFile,
        is_global: bool = False
    ) -> RetrievalFile | None:
        workbook: RetrievalWorkbook = self.get_author_workbook_by_id(
            id=workbook_id, user_email=user_email)
        if "@" in user_email and workbook is not None:
            domain = "default" if is_global else user_email.strip(
            ).lower().split("@")[1]
            vais_service = VertexAISearchService(
                domain=domain,
                chunk_size=workbook.chunk_size
            )

            uploaded_file.data_store_id = workbook.data_store_id
            uploaded_file.chunk_size = workbook.chunk_size
            files_with_existing_name = self._retrieval_file_service.get_files_by_name(
                workbook_id, uploaded_file.name)
            if files_with_existing_name:
                raise GCPExceptions.Conflict(
                    f'File with name {uploaded_file.name} already exists for workbook {workbook_id}')

            retrieval_file: RetrievalFile = self._retrieval_file_service.create_workbook_file(
                workbook_id=workbook_id,
                create_file=uploaded_file
            )

            vais_documents = self._generate_vais_document_for_workbook(
                workbook, [retrieval_file], is_global=is_global)
            vais_service.import_documents_to_datastore(
                data_store_id=workbook.data_store_id,
                documents=vais_documents
            )
            return retrieval_file
        return None

    def callback_upload_files(
        self,
        user_email: str,
        workbook_id: str,
        uploaded_files: List[CreateRetrievalFile]
    ) -> List[RetrievalFile]:
        workbook: RetrievalWorkbook = self.get_author_workbook_by_id(
            id=workbook_id, user_email=user_email)
        if "@" in user_email and workbook is not None:
            domain = user_email.strip().lower().split("@")[1]
            vais_service = VertexAISearchService(
                domain=domain,
                chunk_size=workbook.chunk_size
            )

            for file in uploaded_files:
                file.data_store_id = workbook.data_store_id
                file.chunk_size = workbook.chunk_size

            num_files_created = self._retrieval_file_service.create_workbook_files(
                workbook_id=workbook_id,
                files=uploaded_files
            )

            # TODO: This logic needs to be revisited if this method is ever actually used again
            vais_documents = self._generate_vais_document_for_workbook(
                workbook, uploaded_files)
            vais_service.import_documents_to_datastore(
                data_store_id=workbook.data_store_id,
                documents=vais_documents
            )

            workbook_files = self._retrieval_file_service.get_files_by_workbook_id(
                workbook_id)
            return workbook_files

    def get_vais_document_index_status(
        self,
        workbook: RetrievalWorkbook,
        file_id: str,
        is_global: bool = False
    ) -> bool:
        document_id = RetrievalWorkbookService.get_vais_workbook_document_id(
            workbook, file=file_id, is_global=is_global)
        domain = "default" if is_global else workbook.author.strip(
        ).lower().split("@")[1]
        vais_service = VertexAISearchService(
            domain=domain,
            chunk_size=workbook.chunk_size
        )

        return vais_service.get_document_index_status(document_id)

import React from 'react';
import { MdOutlineDeleteForever, MdCancel } from 'react-icons/md';
import { AiOutlineLoading3Quarters } from 'react-icons/ai';
import { Dialog } from '@base-ui-components/react/dialog';

import './DeleteWorkbookModal.scss';

interface DeleteWorkbookModalProps {
  isOpen: boolean;
  onOpenChange: (value: boolean) => void;
  onDeleteWorkbook: () => void;
  workbookName?: string;
  isDeleting?: boolean;
}

const DeleteWorkbookModal: React.FC<DeleteWorkbookModalProps> = ({
  isOpen,
  onOpenChange,
  onDeleteWorkbook,
  isDeleting = false,
}) => {
  return (
    <Dialog.Root open={isOpen} onOpenChange={onOpenChange}>
      <Dialog.Portal>
        <Dialog.Backdrop className="deleteWorkbookModal__backdrop" />
        <Dialog.Popup className="deleteWorkbookModal__popup">
          {/* Header with icon and message */}
          <div className="deleteWorkbookModal__header">
            <div className="deleteWorkbookModal__icon">
              <MdOutlineDeleteForever size={64} color="#fff" />
            </div>
            <div className="deleteWorkbookModal__content">
              <Dialog.Title className="deleteWorkbookModal__title">Delete this workbook permanently?</Dialog.Title>
              <Dialog.Description className="deleteWorkbookModal__description">
                This workbook cannot be recovered once deleted.
              </Dialog.Description>
            </div>
          </div>

          {/* Action buttons */}
          <div className="deleteWorkbookModal__actions">
            <Dialog.Close
              className="deleteWorkbookModal__cancel-btn"
              render={
                <button>
                  <MdCancel size={24} />
                  <span>No, cancel</span>
                </button>
              }
            />

            <button
              className={`deleteWorkbookModal__delete-btn ${isDeleting ? 'deleteWorkbookModal__delete-btn--deleting' : ''}`}
              onClick={() => onDeleteWorkbook()}
              disabled={isDeleting}
            >
              {isDeleting ? (
                <>
                  <AiOutlineLoading3Quarters size={24} className="deleteWorkbookModal__spinner" />
                  <span>Deleting...</span>
                </>
              ) : (
                <>
                  <MdOutlineDeleteForever size={24} />
                  <span>Yes, delete this workbook</span>
                </>
              )}
            </button>
          </div>
        </Dialog.Popup>
      </Dialog.Portal>
    </Dialog.Root>
  );
};

export default DeleteWorkbookModal;

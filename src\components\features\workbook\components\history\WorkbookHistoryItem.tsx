import React from 'react';
import { MdOutlineChat } from 'react-icons/md';
import { useNavigate } from 'react-router-dom';
import { SidekickWorkbookIcon } from '@/components/common/icons';
import { useThemeStyles } from '@hooks/useThemeStyles';
import { WorkbookHistorySummary } from '../../workbookTypes';

interface WorkbookHistoryItemProps {
  item: WorkbookHistorySummary;
}

const WorkbookHistoryItem: React.FC<WorkbookHistoryItemProps> = ({ item }) => {
  const { classes, isDarkMode } = useThemeStyles();
  const navigate = useNavigate();

  const handleResumeSession = () => {
    navigate(item.resumeUrl);
  };

  return (
    <div className={`workbook-history-item w-full max-w-4xl flex flex-col gap-4 ${classes.text}`}>
      <div className="workbook-history-item__header flex items-center justify-between w-full">
        <div className="workbook-history-item__title-wrapper flex items-center">
          <SidekickWorkbookIcon size={20} className="workbook-history-item__title-icon flex-shrink-0" />
          <span className="workbook-history-item__title ml-2.5 text-xl font-sofia-pro text-[#C5E6FF]">
            {item.title}
          </span>
          <span className="workbook-history-item__created-title ml-1 text-xs font-sofia-pro text-[#C5E6FF]">
            {item.createdTitle}
          </span>
        </div>
        <button
          onClick={handleResumeSession}
          className={`workbook-history-item__resume-button flex items-center h-12 px-3 rounded-full border text-base transition-colors duration-200 text-white gap-2 ${
            isDarkMode
              ? 'border-[#0066B1] hover:bg-[#0066B1] hover:text-white'
              : 'border-blue-600 hover:bg-blue-600 hover:text-white'
          }`}
        >
          <MdOutlineChat size={24} className="workbook-history-item__resume-button-icon w-6 h-6 flex-shrink-0" />
          <span className="workbook-history-item__resume-button-text text-base">Resume Session</span>
        </button>
      </div>

      <div className="workbook-history-item__snippet-container relative pl-[30px] mt-2">
        <div
          className="workbook-history-item__snippet-line absolute left-[10px] top-0 bottom-0 w-0.5 bg-[#0066B1]"
          aria-hidden="true"
        />
        <p className="workbook-history-item__snippet-text text-lg font-roboto truncate">{item.latestMessage}</p>
      </div>
    </div>
  );
};

export default WorkbookHistoryItem;

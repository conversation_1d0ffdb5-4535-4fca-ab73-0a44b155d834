import React from 'react';
import { MdOutlineHistory } from 'react-icons/md';
import { useThemeStyles } from '@hooks/useThemeStyles';

const NoWorkbookHistory: React.FC = () => {
  const { classes } = useThemeStyles();

  return (
    <div className={`no-workbook-history flex flex-col items-center justify-center text-center p-8 ${classes.text}`}>
      <MdOutlineHistory size={64} className={`no-workbook-history__icon mb-4 ${classes.text} opacity-50`} />
      <h2 className={`no-workbook-history__title text-2xl font-semibold mb-2 ${classes.text}`}>
        No Session History
      </h2>
      <p className={`no-workbook-history__description text-lg ${classes.text} opacity-70`}>
        Start a conversation with this workbook to see your session history here.
      </p>
    </div>
  );
};

export default NoWorkbookHistory;

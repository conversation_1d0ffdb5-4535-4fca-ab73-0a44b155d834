/* ===== MODAL LAYOUT ===== */
.create-workbook-modal-overlay {
  position: fixed;
  inset: 0;
  z-index: var(--z-index-backdrop);
  background-color: var(--modal-backdrop-color);
  backdrop-filter: blur(var(--blur-base));
}

.create-workbook {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: var(--z-index-modal);
  width: min(var(--modal-width-lg), 85vw);
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  color: var(--text-text-invert);
  background: var(--elements-surface-surface-brand);
  border-radius: var(--border-radius-border-radius-xxxl);
  overflow: hidden;
  box-shadow: var(--shadow-xl);
  outline: none;

  @media (max-width: 1024px) { 
    width: min(var(--modal-width-md), 90vw); 
    border-radius: var(--border-radius-border-radius-xxl); 
  }
  @media (max-height: 700px) { max-height: 85vh; }
}

.create-workbook__close-btn {
  position: absolute;
  top: var(--spacing-spacing-m-2);
  right: var(--spacing-spacing-m-2);
  background: none;
  border: none;
  color: var(--text-text-invert);
  cursor: pointer;
  padding: var(--spacing-spacing-sm-3);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all var(--transition-normal) ease;
  z-index: 10;

  &:hover { 
    background: var(--modal-hover-background); 
    opacity: var(--opacity-80); 
  }
  &:focus-visible { 
    outline: var(--border-weight-border-weight-m) solid var(--text-text-invert); 
    outline-offset: var(--border-weight-border-weight-m); 
  }
}

.create-workbook__content {
  flex: 1;
  padding: var(--spacing-spacing-l-1) var(--spacing-spacing-l-3) var(--spacing-spacing-m-2);
  display: flex;
  flex-direction: column;
  overflow-y: auto;

  @media (max-width: 1024px) { 
    padding: var(--spacing-spacing-l-2) var(--spacing-spacing-m-3) var(--spacing-spacing-m-1); 
  }
}

.create-workbook__title {
  font-family: var(--font-sofia-pro);
  font-size: var(--font-size-6xl);
  font-weight: var(--font-weight-medium);
  line-height: 1.3;
  color: var(--text-text-invert);
  margin: 0 0 var(--spacing-spacing-l-1) 0;

  @media (max-width: 1024px) { 
    font-size: var(--font-size-4xl); 
    margin-bottom: var(--spacing-spacing-m-3); 
  }
}

/* ===== FORM FIELDS ===== */
%field-base {
  width: 100%;
  background: var(--modal-field-background);
  border: var(--border-weight-border-weight-s) solid var(--border-color-border-dark);
  border-radius: var(--border-radius-border-radius-l);
  color: var(--text-text-invert);
  font-family: var(--font-roboto);
  outline: none;
  transition: border-color var(--transition-normal) ease;

  &::placeholder { color: var(--modal-placeholder-color); }
  &:focus { border-color: var(--border-color-border-interactive); }
  &--with-icon { padding-right: var(--spacing-spacing-l-3); }
}

%field-icon {
  position: absolute;
  right: var(--spacing-spacing-m-1);
  pointer-events: none;
  transition: color var(--transition-normal) ease;

  &--inactive { color: var(--modal-placeholder-color); }
  &--active { color: var(--text-text-invert); }
}

.input-group, .textarea-group {
  position: relative;
  width: 100%;
  margin-bottom: var(--spacing-spacing-m-2);
}

.input-field {
  @extend %field-base;
  padding: var(--spacing-spacing-m-1) var(--spacing-spacing-m-2);
  font-size: var(--font-size-base);
}

.input-field__icon {
  @extend %field-icon;
  top: 50%;
  transform: translateY(-50%);
}

.textarea-field {
  @extend %field-base;
  height: var(--component-height-xl);
  padding: var(--spacing-spacing-sm-3) var(--spacing-spacing-m-3) var(--spacing-spacing-m-3);
  font-size: var(--font-size-sm);
  resize: none;

  @media (max-height: 700px) { height: var(--component-height-lg); }
}

.textarea-field__icon {
  @extend %field-icon;
  top: var(--spacing-spacing-m-1);
}

/* ===== SECTIONS ===== */
.section-title {
  font-family: var(--font-sofia-pro);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-bold);
  color: var(--text-text-invert);
  margin: 0 0 var(--spacing-spacing-sm-3) 0;
  height: var(--section-title-height);
  display: flex;
  align-items: center;
}

.system-instructions .section-title { 
  margin-bottom: 0; 
}

.section-description {
  width: var(--component-width-xl);
  font-family: var(--font-roboto);
  font-size: var(--font-size-xs);
  line-height: 1.2;
  color: var(--modal-text-secondary);
  margin: 0 0 var(--spacing-spacing-sm-3) 0;

  @media (max-width: 1024px) { width: 100%; }
}

/* ===== RADIO ===== */
.chunk-size-section {
  margin-bottom: var(--spacing-spacing-m-2);

  .section-title {
    font-weight: var(--font-weight-medium);
    margin-bottom: var(--spacing-spacing-m-1);
  }
}

.chunk-size-radio-group {
  display: flex;
  align-items: center;
  gap: var(--spacing-spacing-m-3);
  height: var(--component-height-sm);
  padding: var(--spacing-spacing-sm-2);

  &__option {
    display: flex;
    align-items: center;
    gap: var(--spacing-spacing-sm-2);
    color: var(--text-text-invert);
    font-family: var(--font-roboto);
    font-size: var(--font-size-sm);
    cursor: pointer;
  }

  @media (max-width: 1024px) {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-spacing-m-1);
  }
}

.radio-item {
  display: flex;
  width: var(--radio-size);
  height: var(--radio-size);
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  border: var(--border-weight-border-weight-m) solid var(--text-text-invert);
  background-color: transparent;
  transition: background-color var(--transition-normal) ease;

  &:focus-visible {
    outline: var(--border-weight-border-weight-m) solid var(--button-button-primary);
    outline-offset: var(--border-weight-border-weight-m);
  }
}

.radio-indicator {
  display: flex;
  align-items: center;
  justify-content: center;

  &[data-unchecked] { display: none; }

  &::before {
    content: '';
    width: var(--radio-indicator-size);
    height: var(--radio-indicator-size);
    border-radius: 50%;
    background-color: var(--text-text-invert);
  }
}

/* ===== FILE UPLOAD ===== */
.file-upload-section {
  margin-bottom: var(--spacing-spacing-l-1);
}

.add-files-main {
  height: var(--component-height-md);
  display: flex;
  align-items: center;
}

.add-files-button {
  margin-right: var(--spacing-spacing-m-3);
}

.add-files-label {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-spacing-sm-2);
  width: var(--component-width-sm);
  height: var(--component-height-md);
  padding: var(--spacing-spacing-m-1);
  background: var(--button-button-primary);
  border: none;
  border-radius: var(--border-radius-border-radius-m);
  color: var(--text-text-invert);
  font-family: var(--font-roboto);
  font-size: var(--font-size-base);
  cursor: pointer;
  transition: background-color var(--transition-slow) ease;

  &:hover { background-color: var(--button-hover-primary); }
}

.add-files-label-text {
  width: var(--component-width-xs);
}

.add-files-file-list {
  display: flex;
  align-items: center;
  flex: 1;
  overflow-x: auto;
  padding-bottom: var(--spacing-spacing-sm-2);
}

.add-files-file {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-spacing-sm-3);
  margin-right: var(--spacing-spacing-m-2);
  background: var(--modal-field-background);
  border: var(--border-weight-border-weight-s) solid var(--border-color-border-interactive);
  border-radius: var(--border-radius-border-radius-m);
  color: var(--text-text-invert);
  font-family: var(--font-roboto);
  font-size: var(--font-size-xs);
  min-width: var(--component-width-md);
  flex-shrink: 0;
}

.add-files-file-icon, .add-files-file-name, .add-files-file-delete {
  display: flex;
  align-items: center;
}

.add-files-file-icon {
  margin-right: var(--spacing-spacing-sm-2);
}

.add-files-file-name {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-right: var(--spacing-spacing-m-2);
  flex: 1;
}

.add-files-file-delete {
  cursor: pointer;
  transition: opacity var(--transition-normal) ease;

  &:hover { opacity: var(--opacity-70); }
}

/* ===== ACTIONS & FOOTER ===== */
.form-actions {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  margin-top: auto;
}

.btn {
  display: flex;
  align-items: center;
  font-family: var(--font-roboto);
  font-size: var(--font-size-base);
  cursor: pointer;
  border: none;
  transition: all var(--transition-slow) ease;
  outline: none;

  &:focus-visible {
    outline: var(--border-weight-border-weight-m) solid var(--button-button-primary);
    outline-offset: var(--border-weight-border-weight-m);
  }

  &--secondary {
    background: transparent;
    color: var(--text-text-invert);
    padding: var(--spacing-spacing-sm-3) var(--spacing-spacing-m-2);

    &:hover { opacity: var(--opacity-80); }
  }

  &--primary {
    gap: var(--spacing-spacing-sm-3);
    padding: var(--spacing-spacing-m-1) var(--spacing-spacing-m-3);
    background: transparent;
    border: var(--border-weight-border-weight-m) solid var(--border-color-border-interactive);
    border-radius: var(--border-radius-border-radius-full);
    color: var(--text-text-invert);

    &:hover:not(.btn--disabled) {
      background-color: var(--button-button-primary);
    }

    &.btn--disabled {
      cursor: not-allowed;
      opacity: var(--opacity-50);

      &:hover { background-color: transparent; }
    }
  }
}

.create-workbook__footer {
  display: flex;
  align-items: center;
  gap: var(--spacing-spacing-sm-3);
  padding: var(--spacing-spacing-m-2) var(--spacing-spacing-l-3);
  border-top: var(--border-weight-border-weight-s) solid var(--modal-footer-border);
  color: var(--modal-text-secondary);
  font-family: var(--font-roboto);
  font-size: var(--font-size-sm);
  width: 100%;
  flex-shrink: 0;

  @media (max-width: 1024px) { 
    padding: var(--spacing-spacing-m-1) var(--spacing-spacing-m-3); 
  }

  &--error {
    background-color: var(--status-critical-status-critical-tertiary);
    color: var(--status-critical-status-critical-primary);

    svg {
      color: var(--status-critical-status-critical-primary);
    }
  }
}
